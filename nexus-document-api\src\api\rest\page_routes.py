from fastapi import APIRouter, Request, HTTPException, Body
from fastapi.responses import HTMLResponse, JSONResponse
from minio.error import S3Error
from typing import List, Optional
from pydantic import BaseModel
# Sử dụng module mới
from data.db_connection import get_session
from data.repositories.models import Page as PostgresPage, Mgmnt, FetchingStatus
from datetime import datetime
from sqlalchemy import or_
import data.repositories.page_repository2 as page_repository
from typing import Dict, Any
import os
from business.services.page_service import page_service
import traceback
import logging
import asyncio

logger = logging.getLogger(__name__)

router = APIRouter()

cache_time = int(os.getenv("CACHE_TIME", 300))

class PageSimple(BaseModel):
    page_id: str
    title: Optional[str] = None
    parent_id: Optional[int] = None
    space_id: Optional[str] = None
    html_uri: Optional[str] = None
    status: Optional[str] = None
    created_date: Optional[str] = None
    updated_date: Optional[str] = None
    content_type: Optional[str] = None
    is_published: Optional[bool] = None

    class Config:
        from_attributes = True

class MgmntResponse(BaseModel):
    id: int
    user_id: str
    page_id: str
    status: str
    created_date: str
    updated_date: str

    class Config:
        from_attributes = True

class FetchingStatusResponse(BaseModel):
    id: int
    user_id: str
    status: str

    class Config:
        from_attributes = True

class FetchingStatusUpdate(BaseModel):
    status: str
    
@router.get("/api/content/{page_id}", response_class=HTMLResponse)
async def get_html_content_by_page_id(page_id: str, request: Request):
    """Lấy nội dung HTML theo page_id từ PostgreSQL và MinIO"""
    try:
        # Get HTML content using service
        html_content, content_type = await asyncio.to_thread(page_service.get_html_content_by_page_id, page_id)
        # Return HTML response
        return HTMLResponse(content=html_content, media_type=content_type)
    except HTTPException as e:
        # Re-raise HTTP exceptions
        raise HTTPException(status_code=e.status_code, detail=e.detail)
    except Exception as e:
        logger.error(f"Database error: {str(e)}\n{traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")


@router.get("/api/list-pages", response_model=List[PageSimple])
async def list_pages(page_id: Optional[str] = None):
    """
    Lấy danh sách trang
    Có thể lọc theo page_id nếu được cung cấp
    """
    try:
        # Get PostgreSQL session
        session = get_session()
        
        try:
            # ẩn is_published = False
            if page_id is None:
                pages = session.query(PostgresPage).filter(
                    or_(
                        PostgresPage.parent_id == None,
                        PostgresPage.parent_id.notin_(session.query(PostgresPage.page_id))
                    ),
                    PostgresPage.is_published == True
                )
            else:
                pages = session.query(PostgresPage).filter(
                    PostgresPage.parent_id == page_id,
                    PostgresPage.is_published == True   
                )
            
            # Convert datetime objects to strings for JSON serialization
            result = []
            for page in pages:
                page_dict = {
                    "page_id": page.page_id,
                    "title": page.title,
                    "parent_id": page.parent_id,
                    "space_id": page.space_id,
                    "html_uri": page.html_uri,
                    "status": page.status,
                    "created_date": page.created_date.isoformat() if page.created_date else None,
                    "updated_date": page.updated_date.isoformat() if page.updated_date else None,
                    "content_type": page.content_type,
                    "is_published": page.is_published,
                    "type": page.type
                }
                result.append(page_dict)
            
            return result
        except Exception as e:
            logger.error(f"Database error: {str(e)}\n{traceback.format_exc()}")
            raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")
        finally:
            # Always close the session
            session.close()
    except Exception as e:
        logger.error(f"Database error: {str(e)}\n{traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")

@router.get("/api/list-all-pages", response_model=List[Dict[str, Any]])
async def list_all_pages():
    """
    Lấy danh sách tất cả các trang theo cấu trúc cây
    """
    try:
        pages = page_service.get_tree_pages(published_only=True)
        return pages
    except Exception as e:
        logger.error(f"Database error: {str(e)}\n{traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")


@router.get("/api/mgmnt", response_model=List[MgmntResponse])
async def get_all_mgmnt():
    """Lấy tất cả bản ghi quản lý từ PostgreSQL"""
    try:
        session = get_session()
        try:
            mgmnt_records = session.query(Mgmnt).all()
            result = []
            for record in mgmnt_records:
                record_dict = {
                    "id": record.id,
                    "user_id": record.user_id,
                    "page_id": record.page_id,
                    "status": record.status,
                    "created_date": record.created_date.isoformat() if record.created_date else None,
                    "updated_date": record.updated_date.isoformat() if record.updated_date else None
                }
                result.append(record_dict)
            return result
        except Exception as e:
            logger.error(f"Database error: {str(e)}\n{traceback.format_exc()}")
            raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")
        finally:
            session.close()
    except Exception as e:
        logger.error(f"Database error: {str(e)}\n{traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")


@router.get("/api/mgmnt/user/{user_id}", response_model=List[MgmntResponse])
async def get_mgmnt_by_user(user_id: str):
    """Lấy bản ghi quản lý theo user_id từ PostgreSQL"""
    try:
        session = get_session()
        try:
            mgmnt_records = session.query(Mgmnt).filter(Mgmnt.user_id == user_id).all()
            result = []
            for record in mgmnt_records:
                record_dict = {
                    "id": record.id,
                    "user_id": record.user_id,
                    "page_id": record.page_id,
                    "status": record.status,
                    "created_date": record.created_date.isoformat() if record.created_date else None,
                    "updated_date": record.updated_date.isoformat() if record.updated_date else None
                }
                result.append(record_dict)
            return result
        except Exception as e:
            logger.error(f"Database error: {str(e)}\n{traceback.format_exc()}")
            raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")
        finally:
            session.close()
    except Exception as e:
        logger.error(f"Database error: {str(e)}\n{traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")


@router.get("/api/mgmnt/page/{page_id}", response_model=List[MgmntResponse])
async def get_mgmnt_by_page(page_id: str):
    """Lấy bản ghi quản lý theo page_id từ PostgreSQL"""
    try:
        session = get_session()
        try:
            mgmnt_records = session.query(Mgmnt).filter(Mgmnt.page_id == page_id).all()
            result = []
            for record in mgmnt_records:
                record_dict = {
                    "id": record.id,
                    "user_id": record.user_id,
                    "page_id": record.page_id,
                    "status": record.status,
                    "created_date": record.created_date.isoformat() if record.created_date else None,
                    "updated_date": record.updated_date.isoformat() if record.updated_date else None
                }
                result.append(record_dict)
            return result
        except Exception as e:
            logger.error(f"Database error: {str(e)}\n{traceback.format_exc()}")
            raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")
        finally:
            session.close()
    except Exception as e:
        logger.error(f"Database error: {str(e)}\n{traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")


@router.get("/api/mgmnt/{user_id}/{page_id}", response_model=MgmntResponse)
async def get_mgmnt_by_user_and_page(user_id: str, page_id: str):
    """Lấy bản ghi quản lý theo user_id và page_id từ PostgreSQL"""
    try:
        session = get_session()
        try:
            mgmnt_record = session.query(Mgmnt).filter(
                Mgmnt.user_id == user_id,
                Mgmnt.page_id == page_id
            ).first()
            
            if not mgmnt_record:
                raise HTTPException(status_code=404, detail="Management record not found")
            
            return {
                "id": mgmnt_record.id,
                "user_id": mgmnt_record.user_id,
                "page_id": mgmnt_record.page_id,
                "status": mgmnt_record.status,
                "created_date": mgmnt_record.created_date.isoformat() if mgmnt_record.created_date else None,
                "updated_date": mgmnt_record.updated_date.isoformat() if mgmnt_record.updated_date else None
            }
        except Exception as e:
            logger.error(f"Database error: {str(e)}\n{traceback.format_exc()}")
            raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")
        finally:
            session.close()
    except Exception as e:
        logger.error(f"Database error: {str(e)}\n{traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")


@router.get("/api/fetching-status/{user_id}", response_model=FetchingStatusResponse)
async def get_fetching_status(user_id: str):
    """Lấy trạng thái fetch của user từ PostgreSQL"""
    try:
        session = get_session()
        try:
            status = session.query(FetchingStatus).filter(FetchingStatus.user_id == user_id).first()
            
            if not status:
                # Tạo mới nếu chưa có
                status = FetchingStatus(user_id=user_id, status="idle")
                session.add(status)
                session.commit()
                session.refresh(status)
            
            return {
                "id": status.id,
                "user_id": status.user_id,
                "status": status.status
            }
        except Exception as e:
            logger.error(f"Database error: {str(e)}\n{traceback.format_exc()}")
            raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")
        finally:
            session.close()
    except Exception as e:
        logger.error(f"Database error: {str(e)}\n{traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")


@router.put("/api/fetching-status/{user_id}", response_model=FetchingStatusResponse)
async def update_fetching_status(user_id: str, status_update: FetchingStatusUpdate = Body(...)):
    """Cập nhật trạng thái fetch của user trong PostgreSQL"""
    try:
        session = get_session()
        try:
            status = session.query(FetchingStatus).filter(FetchingStatus.user_id == user_id).first()
            
            if not status:
                # Tạo mới nếu chưa có
                status = FetchingStatus(user_id=user_id, status=status_update.status)
                session.add(status)
            else:
                # Cập nhật nếu đã có
                status.status = status_update.status
            
            session.commit()
            session.refresh(status)
            
            return {
                "id": status.id,
                "user_id": status.user_id,
                "status": status.status
            }
        except Exception as e:
            logger.error(f"Database error: {str(e)}\n{traceback.format_exc()}")
            raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")
        finally:
            session.close()
    except Exception as e:
        logger.error(f"Database error: {str(e)}\n{traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")


@router.get("/api/fetching-status", response_model=List[FetchingStatusResponse])
async def get_all_fetching_statuses():
    """Lấy tất cả trạng thái fetch từ PostgreSQL"""
    try:
        session = get_session()
        try:
            statuses = session.query(FetchingStatus).all()
            result = []
            for status in statuses:
                status_dict = {
                    "id": status.id,
                    "user_id": status.user_id,
                    "status": status.status
                }
                result.append(status_dict)
            return result
        except Exception as e:
            logger.error(f"Database error: {str(e)}\n{traceback.format_exc()}")
            raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")
        finally:
            session.close()
    except Exception as e:
        logger.error(f"Database error: {str(e)}\n{traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")
