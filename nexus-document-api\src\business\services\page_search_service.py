from config.config import settings
from httpx import post, delete
from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>
from logging import get<PERSON>ogger
from typing import Dict, Any, Optional
from pydantic import BaseModel
import traceback
logger = getLogger(__name__)

class PageSearchData(BaseModel):
    document_id: str
    title: str
    url: str

class PageDeleteData(BaseModel):
    document_id: str

class SearchService:
    def __init__(self):
        self.full_text_search_service = settings.full_text_search_service

    def send_to_index(self, page_data: PageSearchData):
        try:
            if not self.full_text_search_service:
                logger.warning("Full-text search service is not configured")
                return
            # Chuyển đổi Pydantic model thành dict để có thể serialize sang JSON
            page_data_dict = page_data.model_dump()
            
            logger.info(f"Sending page data to full-text search service: {page_data_dict}")
            # Send to ingest endpoint
            response = post(
                f"{self.full_text_search_service}/api/v1/search-engine/ingest", 
                json=page_data_dict, 
                headers={"Content-Type": "application/json"}, 
                timeout=10
            )
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.exception(f"Error sending page data to full-text search service: {traceback.format_exc()}")
            raise HTTPException(status_code=500, detail=f"Error sending page data to full-text search service: {traceback.format_exc()}")
    def send_to_delete(self, page_data: PageDeleteData):
        try:
            if not self.full_text_search_service:
                logger.warning("Full-text search service is not configured")
                return
            # Extract document_id from the page_data
            document_id = page_data.document_id
            
            logger.info(f"Deleting document with ID {document_id} from full-text search service")
            # Use DELETE method with the pattern /documents/{document_id}
            url = f"{self.full_text_search_service}/api/v1/search-engine/documents/{document_id}"
            response = delete(url, timeout=10)
            response.raise_for_status()
            #warning if response null
            # Check if response has content before trying to parse JSON
            if response.text:
                return response.json()
            if not response.text:
                logger.warning(f"Response is null when deleting document with ID {document_id} from full-text search service")
            return {"status": "success", "message": f"Document {document_id} deleted successfully"}
        except Exception as e:
            logger.exception(f"Error deleting page data from full-text search service: {traceback.format_exc()}")
            raise HTTPException(status_code=500, detail=f"Error deleting page data from full-text search service: {traceback.format_exc()}")