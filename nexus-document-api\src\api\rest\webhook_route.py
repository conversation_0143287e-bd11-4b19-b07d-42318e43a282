from fastapi import Request, HTTPException, BackgroundTasks
from fastapi.responses import JSONResponse
from pydantic import BaseModel, ValidationError
from typing import Optional, Dict, Any
import hashlib
import hmac
import asyncio
import os
import time
import logging
import json
from business.services.page_service import page_service
from fastapi import APIRouter
import traceback
from config.config import settings
router = APIRouter()

logger = logging.getLogger(__name__)
ENABLE_CONFLUENCE_WEBHOOK = os.getenv("ENABLE_CONFLUENCE_WEBHOOK", "false").lower() in ["true", "1", "t"]
WEBHOOK_SECRET = os.getenv("CONFLUENCE_WEBHOOK_SECRET")

# Pydantic models for request validation
class ConfluenceUser(BaseModel):
    accountId: str
    displayName: str
    email: Optional[str] = None

class ConfluencePage(BaseModel):
    idAsString: Optional[str] = None
    creatorAccountId: Optional[str] = None
    spaceKey: Optional[str] = None
    spaceId: Optional[int] = None
    modificationDate: Optional[int] = None
    lastModifierAccountId: Optional[str] = None
    self: Optional[str] = None
    id: Optional[str] = None
    title: Optional[str] = None
    creationDate: Optional[int] = None
    contentType: Optional[str] = None
    version: Optional[int] = None

class ConfluenceWebhookEvent(BaseModel):
    page: Optional[ConfluencePage] = None
    userAccountId: Optional[str] = None
    accountType: Optional[str] = None
    timestamp: Optional[int] = None
    updateTrigger: Optional[str] = None
    suppressNotifications: Optional[bool] = None

def verify_webhook_signature(payload: bytes, signature: str, secret: str) -> bool:
    """Verify webhook signature for security"""
    if not signature or not secret:
        return False
    
    try:
        expected_signature = hmac.new(
            secret.encode('utf-8'),
            payload,
            hashlib.sha256
        ).hexdigest()
        return hmac.compare_digest(f"sha256={expected_signature}", signature)
    except Exception:
        return False
    
async def process_confluence_event(event_data: ConfluenceWebhookEvent) -> None:
    """Process the Confluence webhook event asynchronously"""
    try:
        event_type = event_data.updateTrigger
        
        if settings.confluence_space_key != event_data.page.spaceKey:
            logger.warning(f"Skip processing event for space {event_data.page.spaceKey}")
            return
        
        if event_type == "edit_page":
            await handle_page_updated(event_data)
        else:
            # this is page created
            await handle_page_created(event_data)
            
    except Exception as e:
        logger.error(f"Error processing Confluence event {event_data.updateTrigger}: {str(e)}")
        # Could add retry logic or dead letter queue here

async def process_confluence_event_delete(event_data: ConfluenceWebhookEvent) -> None:
    """Process the Confluence webhook event asynchronously"""
    try:    
        event_type = event_data.updateTrigger
        
        if settings.confluence_space_key != event_data.page.spaceKey:
            logger.warning(f"Skip processing event for space {event_data.page.spaceKey}")
            return
        # safe condition delete page: if event_data before updated_date -> wrong
        page_id = event_data.page.id
        if page_id is None:
            logger.warning(f"Skip processing event for page {page_id} because page not found")
            return
        # TODO: check timestamp before delete page
        await handle_page_removed(event_data)
            
    except Exception as e:
        logger.error(f"Error processing Confluence event {event_data.updateTrigger}: {str(e)}")
        # Could add retry logic or dead letter queue here

async def handle_page_created(event_data: ConfluenceWebhookEvent) -> None:
    """Handle page creation events"""
    logger.info(f"Page created: {event_data.page.title} by {event_data.userAccountId}")
    if ENABLE_CONFLUENCE_WEBHOOK:
        # Trước tiên thực hiện publish_page để cập nhật DB và MinIO
        try:
            logger.info(f"check page infomation: contentType is {event_data.page.contentType}")
            await asyncio.to_thread(page_service.publish_page, event_data.page.id, "html")
        except Exception as e:
            logger.error(f"Error in page creation handling: {str(e)}\nTraceback: {traceback.format_exc()}")

async def handle_page_updated(event_data: ConfluenceWebhookEvent) -> None:
    """Handle page update events"""
    logger.info(f"Page updated: {event_data.page.title} by {event_data.userAccountId}")
    if ENABLE_CONFLUENCE_WEBHOOK:
        # Thực hiện publish_page trước để đảm bảo dữ liệu được cập nhật
        try:
            logger.info(f"check page infomation: contentType is {event_data.page.contentType}")
            await asyncio.to_thread(page_service.publish_page, event_data.page.id, "html")
        except Exception as e:
            logger.error(f"Error in page update handling: {str(e)}\nTraceback: {traceback.format_exc()}")

async def handle_page_removed(event_data: ConfluenceWebhookEvent) -> None:
    """Handle page removal events"""
    logger.info(f"Page removed: {event_data.page.title} by {event_data.userAccountId}")
    if ENABLE_CONFLUENCE_WEBHOOK:
        try:
            # Xóa trang từ database trước
            await asyncio.to_thread(page_service.delete_page, event_data.page.id)
        except Exception as e:
            logger.error(f"Error in page removal handling: {str(e)}\nTraceback: {traceback.format_exc()}")

@router.post("/webhook/confluence/events/delete")
async def handle_confluence_webhook_delete(
    request: Request, 
    background_tasks: BackgroundTasks
):
    """
    just receive event delete page
    """
    try:
        body = await request.body()
        logger.info(f"Received body: {body}")
        if WEBHOOK_SECRET:
            signature = request.headers.get("X-Hub-Signature-256")
            if not verify_webhook_signature(body=body, signature=signature, secret=WEBHOOK_SECRET):
                logger.warning("Invalid webhook signature")
                raise HTTPException(status_code=401, detail="Invalid signature")

        try:
            if not body:
                raise HTTPException(status_code=400, detail="Empty request body")
            raw_data = json.loads(body.decode('utf-8'))
            logger.info(f"Parsed JSON: {raw_data}")
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON payload: {str(e)}")
            raise HTTPException(status_code=400, detail="Invalid JSON payload")
        except UnicodeDecodeError as e:
            logger.error(f"Cannot decode body: {str(e)}")
            raise HTTPException(status_code=400, detail="Cannot decode body")
        try:
            event_data = ConfluenceWebhookEvent(**raw_data)
            if event_data.updateTrigger != "delete_page":
                event_data.updateTrigger = "delete_page"
            logger.info(f"Parsed event data: {event_data}")
        except ValidationError as e:
            logger.error(f"Invalid event data structure: {str(e)}")
            raise HTTPException(status_code=400, detail="Invalid event data structure")
            
        
        # Log the event for monitoring
        logger.info(
            f"Received Confluence webhook - Event: {event_data.updateTrigger}, "
            f"User: {event_data.userAccountId}, "
            f"Page: {event_data.page.title if event_data.page else 'N/A'}"
        )
        background_tasks.add_task(process_confluence_event_delete, event_data)
        
        # Trích xuất thông tin page từ event_data để trả về trong response
        page = event_data.page
        return JSONResponse(
                content={
                    "message": "Webhook received successfully",
                    "updateTrigger": event_data.updateTrigger,
                    "timestamp": event_data.timestamp,
                    "pageId": page.id if page else None,
                    "pageTitle": page.title if page else None,
                    "pageCreatorAccountId": page.creatorAccountId if page else None,
                    "pageSpaceKey": page.spaceKey if page else None,
                    "pageSpaceId": page.spaceId if page else None,
                    "pageModificationDate": page.modificationDate if page else None,
                    "pageLastModifierAccountId": page.lastModifierAccountId if page else None,
                    "pageSelf": page.self if page else None,
                    "pageCreationDate": page.creationDate if page else None,
                    "pageContentType": page.contentType if page else None,
                    "pageVersion": page.version if page else None,
                },
                status_code=200
            )
    except HTTPException:
        # Re-raise HTTP exceptions (these have appropriate status codes)
        raise
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}\n{traceback.format_exc()}")
        raise HTTPException(status_code=500, detail="Internal server error")
@router.post("/webhook/confluence/events")
async def handle_confluence_webhook(
    request: Request, 
    background_tasks: BackgroundTasks
):
    """
    Handle webhook events from Confluence
    
    This endpoint processes various Confluence events like page creation,
    updates, and deletions. Events are processed asynchronously in the background.
    """
    try:
        # Get raw body for signature verification
        body = await request.body()
        logger.info(f"Received body: {body}")
        
        # Verify webhook signature if secret is configured
        if WEBHOOK_SECRET:
            signature = request.headers.get("X-Hub-Signature-256")
            if not verify_webhook_signature(body=body, signature=signature, secret=WEBHOOK_SECRET):
                logger.warning("Invalid webhook signature")
                raise HTTPException(status_code=401, detail="Invalid signature")
        
        # Parse JSON from the body we already read
        try:
            if not body:
                raise HTTPException(status_code=400, detail="Empty request body")
            
            # Parse JSON from raw body instead of calling request.json()
            raw_data = json.loads(body.decode('utf-8'))
            logger.info(f"Parsed JSON: {raw_data}")
            
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON payload: {str(e)}")
            raise HTTPException(status_code=400, detail="Invalid JSON payload")
        except UnicodeDecodeError as e:
            logger.error(f"Cannot decode body: {str(e)}")
            raise HTTPException(status_code=400, detail="Invalid body encoding")
        
        # Validate event data structure
        try:
            event_data = ConfluenceWebhookEvent(**raw_data)
            if event_data.updateTrigger != "edit_page":
                event_data.updateTrigger = "create_page"
            logger.info(f"Parsed event data: {event_data}")
        except ValidationError as e:
            logger.error(f"Invalid event data structure: {str(e)}")
            raise HTTPException(status_code=400, detail="Invalid event data structure")
        
        # Log the event for monitoring
        logger.info(
            f"Received Confluence webhook - Event: {event_data.updateTrigger}, "
            f"User: {event_data.userAccountId}, "
            f"Page: {event_data.page.title if event_data.page else 'N/A'}"
        )
        
        # Process event in background to avoid blocking the response
        background_tasks.add_task(process_confluence_event, event_data)
        
        # Trích xuất thông tin page từ event_data để trả về trong response
        page = event_data.page
        
        return JSONResponse(
            content={
                "message": "Webhook received successfully",
                "updateTrigger": event_data.updateTrigger,
                "timestamp": event_data.timestamp,
                "pageId": page.id if page else None,
                "pageTitle": page.title if page else None,
                "pageCreatorAccountId": page.creatorAccountId if page else None,
                "pageSpaceKey": page.spaceKey if page else None,
                "pageSpaceId": page.spaceId if page else None,
                "pageModificationDate": page.modificationDate if page else None,
                "pageLastModifierAccountId": page.lastModifierAccountId if page else None,
                "pageSelf": page.self if page else None,
                "pageCreationDate": page.creationDate if page else None,
                "pageContentType": page.contentType if page else None,
                "pageVersion": page.version if page else None,
            },
            status_code=200
        )
        
    except HTTPException:
        # Re-raise HTTP exceptions (these have appropriate status codes)
        raise
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}\n{traceback.format_exc()}")
        raise HTTPException(status_code=500, detail="Internal server error")

# Optional: Add a health check endpoint
@router.get("/api/v1/nexus-docs/webhook/confluence/health")
async def confluence_webhook_health():
    """Health check endpoint for the Confluence webhook"""
    return JSONResponse(
        content={
            "status": "healthy",
            "service": "confluence-webhook",
            "timestamp": int(time.time())
        },
        status_code=200
    )