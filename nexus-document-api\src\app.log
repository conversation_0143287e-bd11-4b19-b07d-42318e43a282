2025-06-23 08:53:37,117 - DEBUG - asyncio:64 - Using selector: SelectSelector
2025-06-23 09:51:16,289 - DEBUG - asyncio:64 - Using selector: SelectSelector
2025-06-23 09:52:05,938 - DEBUG - asyncio:64 - Using selector: SelectSelector
2025-06-23 09:52:46,346 - DEBUG - asyncio:64 - Using selector: SelectSelector
2025-06-23 09:53:34,655 - DEBUG - asyncio:64 - Using selector: SelectSelector
2025-06-23 09:55:02,615 - DEBUG - asyncio:64 - Using selector: SelectSelector
2025-06-23 08:55:02,696 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:03,056 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:03,411 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:03,763 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:04,119 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:04,473 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:04,829 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:05,186 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:05,544 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:05,898 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:06,255 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:06,611 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:06,966 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:07,321 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:07,678 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:08,035 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:08,390 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:08,744 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:09,100 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:09,452 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:09,807 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:10,163 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:10,522 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:10,880 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:11,231 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:11,587 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:11,963 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:12,327 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:12,685 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:13,047 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:13,412 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:13,767 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:14,126 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:14,481 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:14,838 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:15,193 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:15,550 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:15,904 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:16,260 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:16,614 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:16,970 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:17,329 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:17,683 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:18,037 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:18,392 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:18,757 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:19,126 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:19,487 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:19,853 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:20,209 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:20,565 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:20,926 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:21,280 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:21,638 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:21,994 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:22,351 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:22,704 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:23,063 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:23,420 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:23,777 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:24,134 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:24,492 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:24,845 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:25,199 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:25,556 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:25,912 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:26,266 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:26,620 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:26,980 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:27,337 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:27,691 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:28,045 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:28,411 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:28,766 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:29,122 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:29,479 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:29,834 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:30,191 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:30,547 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:30,901 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:31,257 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:31,612 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:31,969 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:32,323 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:32,675 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:33,029 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:33,386 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:33,743 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:34,101 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:34,455 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:34,809 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:35,162 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:35,518 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:35,873 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:36,230 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:36,587 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:36,942 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:37,295 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:37,648 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:38,006 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:38,360 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:38,716 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:39,072 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:39,426 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:39,782 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:40,138 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:40,495 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:40,850 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:41,208 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:41,563 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:41,920 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:42,277 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:42,633 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:42,992 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:43,345 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:43,698 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:44,056 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:44,414 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:44,770 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:45,127 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:45,483 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:45,839 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:46,198 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:46,553 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:46,911 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:47,265 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:47,619 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:47,976 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:48,332 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:48,694 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:49,055 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:49,413 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:49,767 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:50,121 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:50,473 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:50,828 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:51,185 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:51,539 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:51,896 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:52,252 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:52,610 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:52,962 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:53,322 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:53,676 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:54,032 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:54,387 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:54,745 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:55,101 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:55,454 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:55,809 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:56,166 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:56,520 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:56,874 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:57,237 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:57,604 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:57,958 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:58,313 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:58,666 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:59,024 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:59,381 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:55:59,735 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:56:00,091 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:56:00,445 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:56:00,800 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:56:01,156 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:56:01,514 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:56:01,869 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:56:02,228 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:56:02,583 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:56:02,935 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:56:03,294 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:56:03,651 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:56:04,004 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:56:04,360 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:56:04,719 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:56:05,078 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:56:05,444 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:56:05,803 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:56:12,792 - DEBUG - asyncio:64 - Using selector: SelectSelector
2025-06-23 08:56:12,898 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:56:13,259 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:56:13,616 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:56:13,970 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:56:14,329 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:56:14,682 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:56:15,037 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:56:15,391 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 08:56:15,749 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 10:21:42,189 - DEBUG - asyncio:64 - Using selector: SelectSelector
2025-06-23 09:21:42,304 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:21:42,665 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:21:43,023 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:21:43,376 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:21:43,732 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:21:44,089 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:21:44,444 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:21:44,801 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:21:45,159 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:21:45,515 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:21:45,869 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:21:46,226 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:21:46,583 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:21:46,937 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:21:47,295 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:21:47,651 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:21:48,004 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:21:48,362 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:21:48,717 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:21:49,073 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:21:49,426 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:21:49,782 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:21:50,140 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:21:50,496 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:21:50,853 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:21:51,210 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:21:51,572 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:21:51,927 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:21:52,280 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:21:52,634 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:21:52,990 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:21:53,345 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:21:53,701 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:21:54,057 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:21:54,413 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:21:54,771 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:21:55,126 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:21:55,482 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:21:55,838 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:21:56,193 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:21:56,550 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:21:56,909 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:21:57,264 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:21:57,620 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:21:57,975 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:21:58,328 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:21:58,680 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:21:59,037 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:21:59,391 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:21:59,748 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:00,103 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:00,458 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:00,816 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:01,173 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:01,527 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:01,882 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:02,238 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:02,593 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:02,948 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:03,305 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:03,663 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:04,037 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:04,396 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:04,753 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:05,108 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:05,462 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:05,816 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:06,170 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:06,526 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:06,882 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:07,237 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:07,602 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:07,957 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:08,315 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:08,669 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:09,026 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:09,380 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:09,738 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:10,092 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:10,454 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:10,812 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:11,168 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:11,524 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:11,879 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:12,236 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:12,593 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:12,947 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:13,302 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:13,659 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:14,015 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:14,372 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:14,728 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:15,085 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:15,438 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:15,797 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:16,152 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:16,507 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:16,862 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:17,218 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:17,574 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:17,930 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:18,289 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:18,647 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:19,007 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:19,362 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:19,716 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:20,073 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:20,428 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:20,784 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:21,138 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:21,494 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:21,850 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:22,207 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:22,563 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:22,919 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:23,275 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:23,632 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:23,989 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:24,346 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:24,702 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:25,058 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:25,417 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:25,775 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:26,130 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:26,485 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:26,840 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:27,199 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:27,553 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:27,910 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:28,266 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:28,622 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:28,977 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:29,333 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:29,689 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:30,055 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:30,413 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:30,766 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:31,123 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:31,481 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:31,839 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:32,197 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:32,553 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:32,908 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:33,264 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:33,622 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:33,979 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:34,335 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:34,709 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:35,064 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:35,422 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:35,777 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:36,132 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:36,489 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:36,847 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:37,205 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:37,562 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:37,915 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:38,269 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:38,621 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:38,979 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:39,336 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:39,690 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:40,046 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:40,404 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:40,779 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:41,162 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:41,537 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:41,893 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:42,246 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:42,599 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:42,955 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:43,312 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:43,672 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:44,029 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:44,386 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:44,741 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:45,095 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:45,453 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:45,810 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:46,164 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:46,519 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:46,878 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:47,235 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:47,594 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:47,949 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:48,307 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:48,662 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:49,018 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:49,390 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:49,743 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:50,100 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:50,456 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:50,812 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:51,168 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:51,524 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:51,881 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:52,236 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:52,592 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:52,946 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:53,305 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:53,677 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:54,051 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:54,407 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:54,762 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:55,120 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:55,478 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:55,838 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:56,190 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:56,546 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:56,905 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:57,260 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:57,620 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:57,977 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:58,335 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:58,693 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:59,049 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:59,406 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:22:59,762 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:23:00,119 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:23:00,474 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:23:00,829 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:23:01,184 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:23:01,540 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 10:24:06,836 - DEBUG - asyncio:64 - Using selector: SelectSelector
2025-06-23 09:24:06,961 - DEBUG - watchfiles.main:306 - 6 changes detected: {(<Change.deleted: 3>, 'D:\\VNG-git\\nexus-document-api\\src\\__pycache__\\main.cpython-313.pyc.1963306355264'), (<Change.deleted: 3>, 'D:\\VNG-git\\nexus-document-api\\src\\__pycache__\\main.cpython-313.pyc'), (<Change.added: 1>, 'D:\\VNG-git\\nexus-document-api\\src\\__pycache__\\main.cpython-313.pyc.1963306355264'), (<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\__pycache__'), (<Change.added: 1>, 'D:\\VNG-git\\nexus-document-api\\src\\__pycache__\\main.cpython-313.pyc'), (<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:24:07,320 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:24:07,679 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:24:08,038 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:24:08,394 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:24:08,750 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:24:09,106 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:24:09,463 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:24:09,819 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:24:10,177 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:24:10,530 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:24:10,887 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:24:11,242 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:24:11,598 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:24:11,951 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:24:12,306 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:24:12,664 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:24:13,019 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:24:13,384 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:24:13,741 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:24:14,100 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:24:14,457 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:24:14,812 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:24:15,166 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:24:15,524 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:24:15,877 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:24:16,234 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:24:16,586 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:24:16,945 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:24:17,304 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:24:17,658 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:24:18,011 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:24:18,366 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:24:18,723 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:24:19,081 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:24:19,434 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:24:19,790 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:24:20,145 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:24:20,499 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:24:20,856 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:24:21,214 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:24:21,569 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:24:21,924 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:24:22,278 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:24:22,635 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:24:22,988 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:24:23,345 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:24:23,701 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:24:24,061 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:24:24,417 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:24:24,775 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:24:25,131 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:24:25,490 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 10:24:39,265 - DEBUG - asyncio:64 - Using selector: SelectSelector
2025-06-23 09:24:39,395 - DEBUG - watchfiles.main:306 - 6 changes detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log'), (<Change.deleted: 3>, 'D:\\VNG-git\\nexus-document-api\\src\\__pycache__\\main.cpython-313.pyc.1641511619136'), (<Change.deleted: 3>, 'D:\\VNG-git\\nexus-document-api\\src\\__pycache__\\main.cpython-313.pyc'), (<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\__pycache__'), (<Change.added: 1>, 'D:\\VNG-git\\nexus-document-api\\src\\__pycache__\\main.cpython-313.pyc'), (<Change.added: 1>, 'D:\\VNG-git\\nexus-document-api\\src\\__pycache__\\main.cpython-313.pyc.1641511619136')}
2025-06-23 09:24:39,750 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:24:40,110 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:24:40,466 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:24:40,822 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 10:24:52,266 - DEBUG - asyncio:64 - Using selector: SelectSelector
2025-06-23 09:24:52,364 - DEBUG - watchfiles.main:306 - 6 changes detected: {(<Change.deleted: 3>, 'D:\\VNG-git\\nexus-document-api\\src\\__pycache__\\main.cpython-313.pyc.2558932585024'), (<Change.deleted: 3>, 'D:\\VNG-git\\nexus-document-api\\src\\__pycache__\\main.cpython-313.pyc'), (<Change.added: 1>, 'D:\\VNG-git\\nexus-document-api\\src\\__pycache__\\main.cpython-313.pyc'), (<Change.added: 1>, 'D:\\VNG-git\\nexus-document-api\\src\\__pycache__\\main.cpython-313.pyc.2558932585024'), (<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\__pycache__'), (<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:24:52,723 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:24:53,077 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:24:53,435 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:24:53,793 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:24:54,148 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:24:54,505 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:24:54,861 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:24:55,217 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:24:55,572 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:24:55,929 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:24:56,286 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:24:56,640 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:24:56,998 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:24:57,357 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 09:24:57,711 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 14:52:36,046 - DEBUG - asyncio:64 - Using selector: SelectSelector
2025-06-23 13:52:36,162 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:52:36,523 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:52:36,881 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:52:37,242 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:52:37,597 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:52:37,950 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:52:38,308 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:52:38,661 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:52:39,014 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:52:39,372 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:52:39,727 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:52:40,084 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:52:40,440 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:52:40,795 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:52:41,152 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:52:41,506 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:52:41,864 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:52:42,217 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:52:42,573 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:52:42,929 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:52:43,282 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:52:43,640 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:52:43,995 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:52:44,351 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:52:44,705 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:52:45,063 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:52:45,420 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:52:45,777 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:52:46,132 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:52:46,499 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:52:46,854 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:52:47,211 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:52:47,616 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:52:47,970 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:52:48,329 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:52:48,688 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:52:49,042 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:52:49,396 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:52:49,748 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:52:50,102 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:52:50,458 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:52:50,810 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:52:51,170 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:52:51,537 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:52:51,891 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:52:52,249 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:52:52,601 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:52:52,959 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:52:53,314 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:52:53,672 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:52:54,030 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:52:54,384 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:52:54,737 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:52:55,092 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:52:55,451 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:52:55,803 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:52:56,168 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:52:56,521 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:52:56,884 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:52:57,237 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:52:57,588 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:52:57,943 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:52:58,297 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:52:58,649 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:52:59,006 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:52:59,362 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:52:59,715 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:53:00,071 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:53:00,436 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:53:00,801 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:53:01,166 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:53:01,531 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:53:01,897 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:53:02,252 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:53:02,615 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:53:02,978 - DEBUG - watchfiles.main:306 - 2 changes detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\main.py'), (<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:53:03,720 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 14:53:05,370 - DEBUG - asyncio:64 - Using selector: SelectSelector
2025-06-23 13:53:05,487 - DEBUG - watchfiles.main:306 - 6 changes detected: {(<Change.deleted: 3>, 'D:\\VNG-git\\nexus-document-api\\src\\__pycache__\\main.cpython-313.pyc'), (<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\__pycache__'), (<Change.deleted: 3>, 'D:\\VNG-git\\nexus-document-api\\src\\__pycache__\\main.cpython-313.pyc.2585541511744'), (<Change.added: 1>, 'D:\\VNG-git\\nexus-document-api\\src\\__pycache__\\main.cpython-313.pyc'), (<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log'), (<Change.added: 1>, 'D:\\VNG-git\\nexus-document-api\\src\\__pycache__\\main.cpython-313.pyc.2585541511744')}
2025-06-23 13:53:05,854 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:53:06,219 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:53:06,582 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:53:06,947 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:53:07,300 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:53:07,665 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:53:08,021 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:53:08,377 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:53:08,732 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:53:09,086 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:53:09,443 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:53:09,798 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:53:10,156 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:53:10,512 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:53:10,865 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:53:11,226 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:53:11,582 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:53:11,936 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:53:12,291 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:53:12,648 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:53:13,005 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:53:13,359 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:53:13,716 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:53:14,070 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:53:14,437 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:53:14,791 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:53:15,147 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:53:15,505 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:53:15,861 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:53:16,215 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:53:16,571 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:53:16,928 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:53:17,283 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:53:17,638 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:53:17,993 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 13:53:18,351 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 15:02:32,053 - DEBUG - asyncio:64 - Using selector: SelectSelector
2025-06-23 15:03:02,260 - DEBUG - asyncio:64 - Using selector: SelectSelector
2025-06-23 15:03:11,973 - DEBUG - asyncio:64 - Using selector: SelectSelector
2025-06-23 14:03:12,069 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 14:03:12,435 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 14:03:12,791 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 14:03:13,147 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 14:03:13,507 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 14:03:13,865 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 14:03:14,221 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 14:03:14,579 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 14:03:14,938 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 14:03:15,295 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 14:03:15,652 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 14:03:16,007 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 15:03:34,585 - DEBUG - asyncio:64 - Using selector: SelectSelector
2025-06-23 14:03:34,689 - DEBUG - watchfiles.main:306 - 6 changes detected: {(<Change.deleted: 3>, 'D:\\VNG-git\\nexus-document-api\\src\\__pycache__\\main.cpython-313.pyc'), (<Change.added: 1>, 'D:\\VNG-git\\nexus-document-api\\src\\__pycache__\\main.cpython-313.pyc'), (<Change.deleted: 3>, 'D:\\VNG-git\\nexus-document-api\\src\\__pycache__\\main.cpython-313.pyc.2503604210240'), (<Change.added: 1>, 'D:\\VNG-git\\nexus-document-api\\src\\__pycache__\\main.cpython-313.pyc.2503604210240'), (<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log'), (<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\__pycache__')}
2025-06-23 14:03:35,051 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 14:03:35,408 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 14:03:35,767 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 14:03:36,126 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 15:05:07,819 - DEBUG - asyncio:64 - Using selector: SelectSelector
2025-06-23 14:05:07,918 - DEBUG - watchfiles.main:306 - 6 changes detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\__pycache__'), (<Change.deleted: 3>, 'D:\\VNG-git\\nexus-document-api\\src\\__pycache__\\main.cpython-313.pyc.1735569082944'), (<Change.deleted: 3>, 'D:\\VNG-git\\nexus-document-api\\src\\__pycache__\\main.cpython-313.pyc'), (<Change.added: 1>, 'D:\\VNG-git\\nexus-document-api\\src\\__pycache__\\main.cpython-313.pyc'), (<Change.added: 1>, 'D:\\VNG-git\\nexus-document-api\\src\\__pycache__\\main.cpython-313.pyc.1735569082944'), (<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 14:05:08,276 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 14:05:08,636 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 14:05:08,995 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 14:05:09,355 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 14:05:09,712 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 14:05:10,070 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 14:05:10,430 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 15:05:28,989 - DEBUG - asyncio:64 - Using selector: SelectSelector
2025-06-23 14:05:29,086 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 14:05:29,452 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 14:05:29,809 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 14:05:30,165 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 14:05:30,526 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 14:05:30,884 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 15:05:44,466 - DEBUG - asyncio:64 - Using selector: SelectSelector
2025-06-23 14:05:44,586 - DEBUG - watchfiles.main:306 - 6 changes detected: {(<Change.added: 1>, 'D:\\VNG-git\\nexus-document-api\\src\\__pycache__\\main.cpython-313.pyc'), (<Change.added: 1>, 'D:\\VNG-git\\nexus-document-api\\src\\__pycache__\\main.cpython-313.pyc.1958771722816'), (<Change.deleted: 3>, 'D:\\VNG-git\\nexus-document-api\\src\\__pycache__\\main.cpython-313.pyc'), (<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\__pycache__'), (<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log'), (<Change.deleted: 3>, 'D:\\VNG-git\\nexus-document-api\\src\\__pycache__\\main.cpython-313.pyc.1958771722816')}
2025-06-23 14:05:44,944 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 14:05:45,302 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 15:06:18,665 - DEBUG - asyncio:64 - Using selector: SelectSelector
2025-06-23 14:06:18,788 - DEBUG - watchfiles.main:306 - 6 changes detected: {(<Change.added: 1>, 'D:\\VNG-git\\nexus-document-api\\src\\__pycache__\\main.cpython-313.pyc.3166061234752'), (<Change.deleted: 3>, 'D:\\VNG-git\\nexus-document-api\\src\\__pycache__\\main.cpython-313.pyc'), (<Change.added: 1>, 'D:\\VNG-git\\nexus-document-api\\src\\__pycache__\\main.cpython-313.pyc'), (<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\__pycache__'), (<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log'), (<Change.deleted: 3>, 'D:\\VNG-git\\nexus-document-api\\src\\__pycache__\\main.cpython-313.pyc.3166061234752')}
2025-06-23 14:06:19,146 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 14:06:19,503 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 14:06:19,859 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 15:06:41,755 - DEBUG - asyncio:64 - Using selector: SelectSelector
2025-06-23 14:06:41,885 - DEBUG - watchfiles.main:306 - 6 changes detected: {(<Change.added: 1>, 'D:\\VNG-git\\nexus-document-api\\src\\__pycache__\\main.cpython-313.pyc'), (<Change.deleted: 3>, 'D:\\VNG-git\\nexus-document-api\\src\\__pycache__\\main.cpython-313.pyc.2268115012160'), (<Change.added: 1>, 'D:\\VNG-git\\nexus-document-api\\src\\__pycache__\\main.cpython-313.pyc.2268115012160'), (<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\__pycache__'), (<Change.deleted: 3>, 'D:\\VNG-git\\nexus-document-api\\src\\__pycache__\\main.cpython-313.pyc'), (<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 14:06:42,245 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-23 14:06:42,604 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 07:35:44,035 - DEBUG - asyncio:64 - Using selector: SelectSelector
2025-06-24 07:38:08,908 - DEBUG - asyncio:64 - Using selector: SelectSelector
2025-06-24 07:46:54,234 - DEBUG - asyncio:64 - Using selector: SelectSelector
2025-06-24 07:50:34,413 - DEBUG - asyncio:64 - Using selector: SelectSelector
2025-06-24 07:51:41,518 - DEBUG - asyncio:64 - Using selector: SelectSelector
2025-06-24 07:53:26,401 - DEBUG - asyncio:64 - Using selector: SelectSelector
2025-06-24 08:10:19,561 - DEBUG - asyncio:64 - Using selector: SelectSelector
2025-06-24 08:10:44,735 - DEBUG - asyncio:64 - Using selector: SelectSelector
2025-06-24 09:14:32,058 - DEBUG - asyncio:64 - Using selector: SelectSelector
2025-06-24 09:17:27,364 - DEBUG - asyncio:64 - Using selector: SelectSelector
2025-06-24 09:22:31,438 - DEBUG - asyncio:64 - Using selector: SelectSelector
2025-06-24 09:23:37,170 - DEBUG - asyncio:64 - Using selector: SelectSelector
2025-06-24 09:31:04,315 - DEBUG - asyncio:64 - Using selector: SelectSelector
2025-06-24 09:37:26,202 - DEBUG - asyncio:64 - Using selector: SelectSelector
2025-06-24 08:37:26,300 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:26,664 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:27,019 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:27,374 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:27,732 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:28,090 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:28,448 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:28,815 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:29,173 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:29,529 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:29,884 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:30,237 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:30,591 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:30,947 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:31,302 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:31,660 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:32,017 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:32,375 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:32,730 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:33,084 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:33,440 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:33,796 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:34,150 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:34,506 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:34,864 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:35,217 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:35,572 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:35,928 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:36,283 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:36,636 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:36,992 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:37,349 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:37,706 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:38,062 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:38,417 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:38,773 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:39,130 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:39,487 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:39,848 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:40,214 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:40,568 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:40,927 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:41,285 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:41,643 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:42,001 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:42,359 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:42,718 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:43,074 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:43,430 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:43,785 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:44,143 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:44,504 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:44,861 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:45,217 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:45,573 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:45,931 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:46,288 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:46,643 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:47,002 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:47,359 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:47,717 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:48,076 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:48,433 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:48,787 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:49,141 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:49,501 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:49,857 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:50,210 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:50,565 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:50,923 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:51,280 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:51,637 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:52,012 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:52,367 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:52,723 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:53,079 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:53,438 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:53,798 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:54,153 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:54,513 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:54,868 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:55,226 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:55,579 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:55,936 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:56,292 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:56,658 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:57,019 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:57,373 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:57,728 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:58,086 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:58,438 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:58,791 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:59,144 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:59,503 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:37:59,862 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:38:00,216 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:38:00,570 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:38:00,926 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:38:01,284 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:38:01,638 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:38:01,992 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:38:02,347 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:38:02,702 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:38:03,058 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:38:03,415 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:38:03,769 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:38:41,046 - DEBUG - asyncio:64 - Using selector: SelectSelector
2025-06-24 08:38:41,161 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:38:41,512 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:38:41,869 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:38:42,228 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:38:42,580 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:38:42,938 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:38:43,297 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:38:43,654 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:38:44,029 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:54:20,517 - DEBUG - asyncio:64 - Using selector: SelectSelector
2025-06-24 09:54:23,167 - DEBUG - asyncio:64 - Using selector: SelectSelector
2025-06-24 08:54:23,273 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:54:23,632 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:54:23,989 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:54:24,347 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:54:24,703 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:54:25,056 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:58:04,999 - DEBUG - asyncio:64 - Using selector: SelectSelector
2025-06-24 08:58:05,112 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:58:05,474 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:58:05,831 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:58:06,190 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:58:06,550 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:58:06,909 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:58:07,270 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:58:07,627 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:58:07,986 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:58:08,343 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:58:08,701 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:58:09,062 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:58:09,423 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 08:58:09,781 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 10:01:52,247 - DEBUG - asyncio:64 - Using selector: SelectSelector
2025-06-24 09:01:52,330 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:01:52,692 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:01:53,051 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:01:53,410 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:01:53,765 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:01:54,116 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:01:54,471 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 10:08:28,014 - DEBUG - asyncio:64 - Using selector: SelectSelector
2025-06-24 09:08:28,111 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:08:28,464 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:08:28,820 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:08:29,176 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:08:29,536 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:08:29,893 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:08:30,250 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:08:30,609 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:08:30,969 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:08:31,325 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:08:31,686 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:08:32,042 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:08:32,399 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:08:32,758 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:08:33,111 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:08:33,471 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:08:33,827 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:08:34,185 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:08:34,543 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:08:34,901 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:08:35,256 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:08:35,613 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:08:35,970 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:08:36,326 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:08:36,687 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:08:37,042 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:08:37,397 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:08:37,752 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:08:38,108 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 10:42:08,982 - DEBUG - asyncio:64 - Using selector: SelectSelector
2025-06-24 09:42:09,144 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:42:09,501 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:42:09,862 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:42:10,219 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:42:10,576 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:42:10,933 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:42:11,293 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:42:11,650 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:42:12,007 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:42:12,366 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:42:12,725 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:42:13,084 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:42:13,442 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:42:13,798 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:42:14,159 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:42:14,516 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:42:14,874 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:42:15,231 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:42:15,588 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:42:15,947 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:42:16,304 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:42:16,661 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:42:17,020 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:42:17,381 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:42:17,735 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:42:18,093 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:42:18,453 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:42:18,811 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:42:19,167 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:42:19,527 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:42:19,884 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:42:20,239 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 10:50:28,981 - DEBUG - asyncio:64 - Using selector: SelectSelector
2025-06-24 09:50:29,076 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:50:29,433 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:50:29,792 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:50:30,149 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:50:30,507 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:50:30,866 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 10:55:29,511 - DEBUG - asyncio:64 - Using selector: SelectSelector
2025-06-24 09:55:29,611 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:55:29,963 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:55:30,319 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:55:30,678 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:55:31,034 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:55:31,393 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:55:31,753 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:55:32,112 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:55:32,468 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:55:32,825 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:55:33,184 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:55:33,540 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:55:33,897 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:55:34,254 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:55:34,614 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:55:34,972 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:55:35,330 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:55:35,687 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:55:36,045 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:55:36,401 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:55:36,758 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:55:37,116 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:55:37,476 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:55:37,834 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:55:38,190 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:55:38,550 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:55:38,909 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:55:39,265 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 10:55:39,299 - ERROR - api.rest.page_routes:74 - Database error: PageService.get_cache_stats() missing 1 required keyword-only argument: 'kwargs'
Traceback (most recent call last):
  File "D:\VNG-git\nexus-document-api\src\api\rest\page_routes.py", line 67, in get_html_content_by_page_id
    html_content, content_type = await asyncio.to_thread(page_service.get_html_content_by_page_id, page_id)
                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\threads.py", line 25, in to_thread
    return await loop.run_in_executor(None, func_call)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\concurrent\futures\thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
  File "D:\VNG-git\nexus-document-api\src\business\services\page_service.py", line 337, in get_html_content_by_page_id
    return self.get_cache_stats('html', page_id, fetch_html, page_id)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: PageService.get_cache_stats() missing 1 required keyword-only argument: 'kwargs'

2025-06-24 09:55:39,623 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:55:39,981 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:55:40,340 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:55:40,694 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:55:41,053 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:55:41,412 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:55:41,772 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:55:42,128 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:55:42,487 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:55:42,845 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:55:43,203 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:55:43,559 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:55:43,916 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 09:55:44,274 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 11:33:18,746 - DEBUG - asyncio:64 - Using selector: SelectSelector
2025-06-24 10:33:18,843 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 10:33:19,214 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 10:33:19,576 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 10:33:19,941 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 10:33:20,299 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 10:33:20,655 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 10:33:21,010 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 10:33:21,369 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 10:33:21,735 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 10:33:22,092 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 10:33:22,448 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 10:33:22,804 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 10:33:23,165 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 10:33:23,524 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 10:33:23,882 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 10:33:24,239 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 10:33:24,595 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 11:35:00,891 - DEBUG - asyncio:64 - Using selector: SelectSelector
2025-06-24 10:35:01,001 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 10:35:01,360 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 10:35:01,719 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 10:35:02,080 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 10:35:02,440 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 10:35:02,798 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 10:35:03,157 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 10:35:03,512 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 10:35:03,866 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 10:35:04,223 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 10:35:04,580 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 10:35:04,939 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 10:35:05,295 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 10:35:05,653 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 10:35:06,020 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 10:35:06,372 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 10:35:06,727 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 10:35:07,082 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 10:35:07,435 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 10:35:07,792 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 10:35:08,149 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 10:35:08,505 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 10:35:08,857 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 10:35:09,216 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 10:35:09,584 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 10:35:09,942 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 10:35:10,300 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 10:35:10,659 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 10:35:11,019 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 10:35:11,374 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 10:35:11,733 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 10:35:12,088 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 10:35:12,447 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 10:35:12,804 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 10:35:13,163 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 10:35:13,520 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 10:35:13,874 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 10:35:14,228 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 10:35:14,581 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 10:35:14,937 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 10:35:15,292 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 10:35:15,648 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 10:35:16,004 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 10:35:16,359 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 10:35:16,716 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 10:35:17,075 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 10:35:17,431 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 10:35:17,791 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 10:35:18,145 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 10:35:18,502 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 10:35:18,861 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 10:35:19,216 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-24 10:35:19,574 - DEBUG - watchfiles.main:306 - 1 change detected: {(<Change.modified: 2>, 'D:\\VNG-git\\nexus-document-api\\src\\app.log')}
2025-06-26 02:42:03,992 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:42:05,922 - INFO - watchfiles.main:308 - 5 changes detected
2025-06-26 02:42:06,285 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:42:06,641 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:42:06,995 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:42:07,356 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:42:07,711 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:42:08,076 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:42:08,430 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:42:08,785 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:42:09,145 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:42:09,499 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:42:09,856 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:42:10,213 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:42:10,567 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:42:10,921 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:42:11,278 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:42:11,633 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:46:11,963 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:46:13,754 - INFO - watchfiles.main:308 - 5 changes detected
2025-06-26 02:46:14,116 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:46:14,484 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:46:14,841 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:46:15,196 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:46:15,549 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:46:15,907 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:46:16,261 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:46:16,617 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:46:16,974 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:46:17,331 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:46:17,698 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:46:18,051 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:46:18,405 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:46:18,766 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:46:19,122 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:46:19,481 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:46:19,838 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:46:20,197 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:46:20,551 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:46:20,905 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:46:21,259 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:46:21,612 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:46:21,974 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:46:22,333 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:46:22,692 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:46:23,046 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:46:23,400 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:46:23,759 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:46:24,113 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:46:24,469 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:46:24,824 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:46:25,176 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:46:25,534 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:46:25,890 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:46:26,243 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:46:26,600 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:46:26,957 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:46:27,313 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:46:27,668 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:46:28,034 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:46:28,394 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:46:28,749 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:46:29,101 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:46:29,458 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:46:29,812 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:46:30,171 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 03:47:28,440 - INFO - api.rest.markdown_routes:78 - Fetched page from Confluence: 6193372
2025-06-26 03:47:29,441 - INFO - business.services.confluence:25 - Get page 6193372 from Confluence
2025-06-26 03:47:29,443 - INFO - business.services.page_service:63 - Fetched page from Confluence: 6193372
2025-06-26 03:47:29,457 - INFO - business.services.page_service:95 - Content changed: True
2025-06-26 03:47:29,458 - INFO - business.services.page_service:99 - Processing content for page 6193372
2025-06-26 03:47:29,458 - INFO - business.services.html_process:167 - Processing HTML to upload images and update src attributes
2025-06-26 03:47:29,459 - INFO - business.services.html_process:77 - Downloading image from https://vng-team-lv8nohv4.atlassian.net/wiki/download/attachments/6193372/vng.png?api=v2
2025-06-26 03:47:30,737 - INFO - business.services.html_process:77 - Downloading image from https://vng-team-lv8nohv4.atlassian.net/wiki/download/attachments/6193372/download.webp?api=v2
2025-06-26 03:47:31,510 - INFO - business.services.html_process:285 - Downloading file from https://vng-team-lv8nohv4.atlassian.net/wiki/download/attachments/6193372/basic-text.pdf
2025-06-26 03:47:32,828 - INFO - business.services.html_process:285 - Downloading file from https://vng-team-lv8nohv4.atlassian.net/wiki/download/attachments/6193372/0e956ada.html
2025-06-26 03:47:34,016 - INFO - business.services.html_process:178 - Saving HTML to MinIO
2025-06-26 03:47:34,017 - INFO - business.services.html_process:205 - Saving file to MinIO: 74c6ef9a.html
2025-06-26 03:47:34,069 - INFO - business.services.page_service:109 - Saved content to MinIO: 74c6ef9a.html
2025-06-26 03:47:34,076 - INFO - business.services.page_service:147 - Successfully committed page 6193372 to database
2025-06-26 03:47:34,076 - INFO - business.services.page_service:149 - Content changed, sending to full-text search API: 6193372
2025-06-26 03:47:34,076 - WARNING - business.services.page_search_service:25 - Full-text search service is not configured
2025-06-26 03:47:34,076 - INFO - business.services.page_service:322 - Background thread: Successfully sent page 6193372 to full-text search API
2025-06-26 03:47:34,077 - INFO - business.services.page_service:167 - Parent page 131182 found, publishing page 6193372
2025-06-26 03:47:35,296 - INFO - business.services.confluence:25 - Get page 131182 from Confluence
2025-06-26 03:47:35,306 - INFO - business.services.page_service:63 - Fetched page from Confluence: 131182
2025-06-26 03:47:35,311 - INFO - business.services.page_service:95 - Content changed: True
2025-06-26 03:47:35,312 - INFO - business.services.page_service:99 - Processing content for page 131182
2025-06-26 03:47:35,312 - INFO - business.services.html_process:167 - Processing HTML to upload images and update src attributes
2025-06-26 03:47:35,319 - INFO - business.services.html_process:77 - Downloading image from /wiki/s/328570557/6452/81f56a7fee5e405421dbdc8be74de473d1f349d6/_/images/icons/wait.gif
2025-06-26 03:47:35,326 - INFO - business.services.html_process:285 - Downloading file from https://vng-team-lv8nohv4.atlassian.net/wiki/spaces/~712020aa19b5477df648d7b563b227a7ec6726/pages/6193372/asdsafafsa+1234
2025-06-26 03:47:36,889 - INFO - business.services.html_process:285 - Downloading file from https://vng-team-lv8nohv4.atlassian.net/wiki/spaces/~712020aa19b5477df648d7b563b227a7ec6726/pages/6291532/Grow+-+Segment1134
2025-06-26 03:47:38,851 - INFO - business.services.html_process:178 - Saving HTML to MinIO
2025-06-26 03:47:38,851 - INFO - business.services.html_process:205 - Saving file to MinIO: e1693cd6.html
2025-06-26 03:47:38,911 - INFO - business.services.page_service:109 - Saved content to MinIO: e1693cd6.html
2025-06-26 03:47:38,916 - INFO - business.services.page_service:147 - Successfully committed page 131182 to database
2025-06-26 03:47:38,916 - INFO - business.services.page_service:149 - Content changed, sending to full-text search API: 131182
2025-06-26 03:47:38,917 - WARNING - business.services.page_search_service:25 - Full-text search service is not configured
2025-06-26 03:47:38,917 - INFO - business.services.page_service:322 - Background thread: Successfully sent page 131182 to full-text search API
2025-06-26 03:47:38,917 - WARNING - business.services.page_service:170 - Parent page None not found, skipping publish
2025-06-26 03:47:38,917 - INFO - business.services.page_service:376 - Updating tree pages cache: published_only_True - force_refresh: True
2025-06-26 03:47:38,917 - INFO - data.repositories.page_repository2:99 - Get tree pages from database...
2025-06-26 03:47:38,926 - INFO - business.services.page_service:399 - Tree pages cache refreshed
2025-06-26 03:47:38,926 - INFO - business.services.page_service:200 - Page 131182 published in 4.849336 seconds
2025-06-26 03:47:38,926 - INFO - business.services.page_service:376 - Updating tree pages cache: published_only_True - force_refresh: True
2025-06-26 03:47:38,927 - INFO - data.repositories.page_repository2:99 - Get tree pages from database...
2025-06-26 03:47:38,930 - INFO - business.services.page_service:399 - Tree pages cache refreshed
2025-06-26 03:47:38,930 - INFO - business.services.page_service:200 - Page 6193372 published in 10.489528 seconds
2025-06-26 03:47:38,931 - INFO - api.rest.markdown_routes:82 - Invalidated cache tree pages
2025-06-26 02:55:45,797 - INFO - watchfiles.main:308 - 2 changes detected
2025-06-26 02:55:46,548 - INFO - watchfiles.main:308 - 2 changes detected
2025-06-26 02:55:46,906 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:55:47,264 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:55:47,622 - INFO - watchfiles.main:308 - 6 changes detected
2025-06-26 02:55:47,981 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:55:48,335 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:55:48,686 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:55:49,039 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:55:49,394 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:55:49,750 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:55:50,106 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:55:50,464 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:55:50,819 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:55:51,176 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:55:51,534 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:55:51,890 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:55:52,250 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:55:52,604 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:55:52,962 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:55:53,318 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:55:53,676 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:55:54,030 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:55:54,389 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:55:54,746 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:55:55,102 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:55:55,458 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:55:55,815 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:55:56,172 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:55:56,529 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:55:56,885 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:55:57,243 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:55:57,599 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:55:57,951 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:55:58,305 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:55:58,662 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:55:59,118 - INFO - watchfiles.main:308 - 4 changes detected
2025-06-26 02:56:00,563 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:56:00,925 - INFO - watchfiles.main:308 - 6 changes detected
2025-06-26 02:56:01,279 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:56:01,636 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:56:01,990 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:56:02,343 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:56:02,699 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:56:03,055 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:56:03,414 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:56:03,769 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 02:56:04,126 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 03:07:53,153 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 03:07:55,114 - INFO - watchfiles.main:308 - 5 changes detected
2025-06-26 03:07:55,472 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 03:07:55,830 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 03:07:56,181 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 03:07:56,540 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 03:07:56,893 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 03:07:57,247 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 03:07:57,651 - INFO - watchfiles.main:308 - 2 changes detected
2025-06-26 03:07:58,388 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 03:07:59,300 - INFO - watchfiles.main:308 - 5 changes detected
2025-06-26 03:07:59,656 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 03:08:00,009 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 03:08:00,367 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 03:08:00,723 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 03:08:01,078 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 03:08:01,434 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 03:08:01,787 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 03:08:02,142 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 03:08:02,500 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 03:08:02,853 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 03:08:03,210 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 03:08:03,566 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 03:08:03,922 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 03:08:04,281 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 03:08:04,636 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 03:08:04,990 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 03:08:05,346 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 03:08:05,701 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 03:08:06,060 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 03:08:06,415 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 03:08:06,775 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 03:08:07,129 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 03:08:07,486 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 03:08:07,842 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 03:08:08,198 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 03:08:08,552 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 03:08:08,907 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 03:08:09,265 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 03:08:09,623 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 03:08:09,992 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 03:08:10,349 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 03:11:17,161 - INFO - watchfiles.main:308 - 3 changes detected
2025-06-26 03:11:19,002 - INFO - watchfiles.main:308 - 5 changes detected
2025-06-26 03:11:19,361 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 03:11:19,716 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 03:11:20,072 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 03:11:20,427 - INFO - watchfiles.main:308 - 1 change detected
2025-06-26 03:11:20,780 - INFO - watchfiles.main:308 - 1 change detected
