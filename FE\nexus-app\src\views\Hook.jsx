import React, { useEffect, useState } from "react";
import axios from "axios";
import {
  <PERSON><PERSON>,
  Container,
  Table,
  Form,
  Modal,
  <PERSON><PERSON>,
  Spin<PERSON>,
  Card
} from "react-bootstrap";
import { FaTrash, FaPencilAlt, FaPlus } from "react-icons/fa";

const HookManagement = () => {
  const [hooks, setHooks] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [currentHook, setCurrentHook] = useState(null);
  const [formData, setFormData] = useState({
    confluenceUrl: "",
    apiToken: "",
    email: "",
    description: "",
    enabled: true
  });
  const [formError, setFormError] = useState("");

  // API base URL - should be configured based on environment
  const API_BASE_URL = process.env.REACT_APP_API_URL || "http://localhost:5000";

  useEffect(() => {
    fetchHooks();
  }, []);

  const fetchHooks = async () => {
    setLoading(true);
    try {
      const response = await axios.get(`${API_BASE_URL}/api/v1/webhooks`);
      setHooks(response.data);
      setError(null);
    } catch (err) {
      console.error("Error fetching hooks:", err);
      // setError(`Failed to load hooks: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === "checkbox" ? checked : value
    });
  };

  const resetForm = () => {
    setFormData({
      confluenceUrl: "",
      apiToken: "",
      email: "",
      description: "",
      enabled: true
    });
    setFormError("");
  };

  const openAddModal = () => {
    resetForm();
    setEditMode(false);
    setShowModal(true);
  };

  const openEditModal = (hook) => {
    setFormData({
      confluenceUrl: hook.confluenceUrl,
      apiToken: hook.apiToken,
      email: hook.email,
      description: hook.description || "",
      enabled: hook.enabled
    });
    setCurrentHook(hook);
    setEditMode(true);
    setShowModal(true);
  };

  const closeModal = () => {
    setShowModal(false);
    resetForm();
  };

  const validateForm = () => {
    if (!formData.confluenceUrl) {
      setFormError("Confluence URL is required");
      return false;
    }
    if (!formData.apiToken) {
      setFormError("API Token is required");
      return false;
    }
    if (!formData.email) {
      setFormError("Email is required");
      return false;
    }
    return true;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      if (editMode) {
        await axios.put(`${API_BASE_URL}/api/v1/webhooks/${currentHook.id}`, formData);
      } else {
        await axios.post(`${API_BASE_URL}/api/v1/webhooks`, formData);
      }
      
      closeModal();
      fetchHooks();
    } catch (err) {
      console.error("Error saving hook:", err);
      setFormError(`Failed to save hook: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (hookId) => {
    if (window.confirm("Are you sure you want to delete this webhook?")) {
      setLoading(true);
      try {
        await axios.delete(`${API_BASE_URL}/api/v1/webhooks/${hookId}`);
        fetchHooks();
      } catch (err) {
        console.error("Error deleting hook:", err);
        setError(`Failed to delete hook: ${err.message}`);
      } finally {
        setLoading(false);
      }
    }
  };

  const maskToken = (token) => {
    if (!token) return "";
    if (token.length <= 8) return "********";
    return token.substring(0, 4) + "..." + token.substring(token.length - 4);
  };

  return (
    <Container className="py-4">
      <Card className="shadow-sm">
        <Card.Header className="bg-primary text-white d-flex justify-content-between align-items-center">
          <h2 className="mb-0">Webhook Management</h2>
          <Button variant="light" onClick={openAddModal}>
            <FaPlus /> Add New Hook
          </Button>
        </Card.Header>
        <Card.Body>
          {error && <Alert variant="danger">{error}</Alert>}
          
          {loading && !showModal ? (
            <div className="text-center py-5">
              <Spinner animation="border" variant="primary" />
              <p className="mt-3">Loading webhooks...</p>
            </div>
          ) : (
            <Table striped bordered hover responsive>
              <thead>
                <tr>
                  <th>#</th>
                  <th>Confluence URL</th>
                  <th>Email</th>
                  <th>API Token</th>
                  <th>Description</th>
                  <th>Status</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {hooks.length === 0 ? (
                  <tr>
                    <td colSpan="7" className="text-center py-4">
                      {/* No webhooks found. Click "Add New Hook" to create one. */}
                    </td>
                  </tr>
                ) : (
                  hooks.map((hook, index) => (
                    <tr key={hook.id}>
                      <td>{index + 1}</td>
                      <td>{hook.confluenceUrl}</td>
                      <td>{hook.email}</td>
                      <td>{maskToken(hook.apiToken)}</td>
                      <td>{hook.description || "-"}</td>
                      <td>
                        <span className={`badge bg-${hook.enabled ? "success" : "danger"}`}>
                          {hook.enabled ? "Active" : "Inactive"}
                        </span>
                      </td>
                      <td>
                        <Button 
                          variant="outline-primary" 
                          size="sm" 
                          className="me-2" 
                          onClick={() => openEditModal(hook)}
                        >
                          <FaPencilAlt />
                        </Button>
                        <Button 
                          variant="outline-danger" 
                          size="sm" 
                          onClick={() => handleDelete(hook.id)}
                        >
                          <FaTrash />
                        </Button>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </Table>
          )}
        </Card.Body>
      </Card>

      {/* Add/Edit Hook Modal */}
      <Modal show={showModal} onHide={closeModal} backdrop="static" size="lg">
        <Modal.Header closeButton>
          <Modal.Title>{editMode ? "Edit Webhook" : "Add New Webhook"}</Modal.Title>
        </Modal.Header>
        <Form onSubmit={handleSubmit}>
          <Modal.Body>
            {formError && <Alert variant="danger">{formError}</Alert>}
            
            <Form.Group className="mb-3">
              <Form.Label>Confluence URL <span className="text-danger">*</span></Form.Label>
              <Form.Control
                type="url"
                name="confluenceUrl"
                value={formData.confluenceUrl}
                onChange={handleInputChange}
                placeholder="vng-team-lv8nohv4.atlassian.net"
                required
              />
              <Form.Text className="text-muted">
                The base URL of your Confluence instance
              </Form.Text>
            </Form.Group>

            <Form.Group className="mb-3">
              <Form.Label>API Token <span className="text-danger">*</span></Form.Label>
              <Form.Control
                type="password"
                name="apiToken"
                value={formData.apiToken}
                onChange={handleInputChange}
                placeholder="Enter your Confluence API token"
                required
              />
              <Form.Text className="text-muted">
                Create an API token from your Atlassian account settings
              </Form.Text>
            </Form.Group>

            <Form.Group className="mb-3">
              <Form.Label>Email <span className="text-danger">*</span></Form.Label>
              <Form.Control
                type="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                placeholder="<EMAIL>"
                required
              />
              <Form.Text className="text-muted">
                The email associated with your Atlassian account
              </Form.Text>
            </Form.Group>

            <Form.Group className="mb-3">
              <Form.Label>Description</Form.Label>
              <Form.Control
                as="textarea"
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                placeholder="Optional description for this webhook"
                rows={2}
              />
            </Form.Group>

            <Form.Group className="mb-3">
              <Form.Check
                type="checkbox"
                name="enabled"
                label="Enable this webhook"
                checked={formData.enabled}
                onChange={handleInputChange}
              />
            </Form.Group>
          </Modal.Body>
          <Modal.Footer>
            <Button variant="secondary" onClick={closeModal}>
              Cancel
            </Button>
            <Button variant="primary" type="submit" disabled={loading}>
              {loading ? (
                <>
                  <Spinner as="span" animation="border" size="sm" className="me-2" />
                  Saving...
                </>
              ) : (
                "Save Webhook"
              )}
            </Button>
          </Modal.Footer>
        </Form>
      </Modal>
    </Container>
  );
};

export default HookManagement;