from typing import Optional
from sqlalchemy.orm import Session
from data.repositories import page_repository2
# Sử dụng module mới
from data.db_connection import get_session

class UserService:
    def __init__(self, db: Session):
        """
        Initialize the UserService.
        This service is responsible for handling user-related operations.
        """
        self.db = db
    
    def list_pages(self, authorization: str, parent_id: Optional[int]) -> list:
        """
        List all pages for the user with pagination.
        :param authorization: Authorization token
        :param parent_id: Optional parent ID to filter pages
        :return: List of pages
        """
        # check authorization token if needed
        if not authorization:
            raise ValueError("Authorization token is required")
        # pagination and filtering logic can be added here
        return page_repository2.get_all_pages(self.db, parent_id = parent_id)
    def get_page_by_page_id(self, authorization: str, page_id: int) -> dict:
        """ Get a specific page by its ID.
        :param page_id: ID of the page to retrieve
        :param authorization: Authorization token
        :return: Page details (id, title, content_type, content, created_date, author, status)
        """
        # check authorization token if needed
        if not authorization:
            raise ValueError("Authorization token is required")
        # Placeholder for actual implementation
        page = page_repository2.get_page_by_page_id(self.db, page_id)
        if not page:
            raise ValueError(f"Page with ID {page_id} not found")
        return {
            "id": page.get("id"),
            "title": page.get("title"),
            "html_uri": page.get("html_uri"),
            "page_id": page.get("page_id"),
            "parent_id": page.get("parent_id"),
            "space_id": page.get("space_id"),
            "created_date": page.get("created_date", ""),
            "updated_date": page.get("updated_date", ""),
            "status": page.get("status"),
        }


