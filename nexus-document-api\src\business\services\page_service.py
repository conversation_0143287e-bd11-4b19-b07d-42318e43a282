from data.db_connection import get_session
import data.repositories.page_repository2 as page_repository
from fastapi import HTTPException
import logging
from business.services.confluence import get_confluence_page
from business.services.html_process import save_processed_html_to_minio
from business.services.kb_client import sync_to_kb
import core.common as common_util
from data.repositories.models import Page
from business.services.minio_client import create_minio_client
from sqlalchemy.exc import SQLAlchemyError
from datetime import datetime
from typing import Dict, Any, List, Tuple, Optional
from business.services.page_search_service import PageSearchData, PageDeleteData
from minio.error import S3Error
import requests
import json
import os
import traceback
from business.services.page_search_service import SearchService
from fastapi import BackgroundTasks
from config.config import settings
import data.repositories.page_repository2 as page_repository
from cachetools import TTLCache
from cachetools.keys import hashkey
import threading
from threading import RLock
import time


logger = logging.getLogger(__name__)

class PageService:
    def __init__(self):
        self.minio_client = create_minio_client()
        self.minio_bucket = settings.minio_bucket_name
        self.search_service = SearchService()
        self.page_repo = page_repository
        self.tree_cache = TTLCache(maxsize=100, ttl=settings.cache_time)
        self.cache_lock = RLock()  # Reentrant lock for thread-safety
        
    def publish_page(self, page_id: str, exported_type: str = "html", cookies: Optional[List[Dict[str, str]]] = None):
        
        if not page_id:
            raise ValueError("Page ID is required")
        
        if exported_type not in ["html", "markdown"]:
            raise ValueError("Invalid exported type. Must be 'html' or 'markdown'")
        
        operation_start = datetime.now()
        filename = None
        meta = None
        file_uri = ""
        hash_content = ""
        title = ""
        current_date = common_util.get_current_date()
        created_date = current_date
        updated_date = current_date
        
        with get_session() as db:
            try:
                # Fetch page from Confluence
                data = get_confluence_page(page_id)
                logger.info(f"Fetched page from Confluence: {page_id}")

                if not data:
                    raise ValueError("Page not found: " + str(page_id))
                
                # Extract page data
                content_data = data.get('body', {}).get('export_view', {}).get('value', '')
                    
                title = data.get('title', '')
                page_type = data.get('type', '')
                
                # Extract hierarchy info
                ancestors = data.get("ancestors", [])
                parent_id = None
                if ancestors and isinstance(ancestors[-1], dict):
                    parent_id = ancestors[-1].get("id")
                space_id = data.get("space", {}).get("id")
                
                # Generate content hash
                hash_content = common_util.md5_hash(content_data)
                title = common_util.clean_string(title)
                
                # Check if page exists and needs update
                existing_page = db.query(Page).filter(Page.page_id == page_id).first()
                
                is_content_changed = (
                    not existing_page or
                    not existing_page.hash_content or 
                    existing_page.hash_content != hash_content
                )
                
                
                logger.info(f"Content changed: {is_content_changed}")
                
                # Process content if needed
                if is_content_changed:
                    logger.info(f"Processing content for page {page_id}")
                    try:
                        filename, meta = save_processed_html_to_minio(
                            content_data, 
                            self.minio_client, 
                            self.minio_bucket, 
                            content_type=exported_type,
                            cookies=cookies
                        )
                        # Lấy content_type từ kết quả meta
                        logger.info(f"Saved content to MinIO: {filename}")
                    except Exception as e:
                        logger.error(f"Failed to save to MinIO: {e}\nTraceback: {traceback.format_exc()}")
                        raise HTTPException(status_code=500, detail="Failed to save content to storage")
                # Update or create page record
                if existing_page:
                    # Update existing page
                    existing_page.space_id = space_id
                    existing_page.title = title
                    existing_page.type = page_type
                    existing_page.updated_date = updated_date
                    existing_page.parent_id = parent_id
                    existing_page.status = "active"
                    existing_page.is_published = True
                    if is_content_changed and filename:
                        existing_page.html_uri = filename
                        existing_page.hash_content = hash_content
                        if meta and meta.get('content_type') and meta.get('content_type') != existing_page.content_type:
                            existing_page.content_type = meta.get('content_type')
                else:
                    # Create new page
                    new_page = Page(
                        page_id=page_id,
                        title=title,
                        parent_id=parent_id,
                        space_id=space_id,
                        html_uri=filename,
                        created_date=created_date,
                        updated_date=updated_date,
                        status='active',
                        hash_content=hash_content,
                        is_published=True,
                        type=page_type,
                        content_type=meta.get('content_type', 'text/html') if meta else 'text/html'
                    )
                    db.add(new_page)
                # Commit all changes
                db.commit()
                logger.info(f"Successfully committed page {page_id} to database")
                if is_content_changed:
                    logger.info(f"Content changed, sending to full-text search API: {page_id}")
                    bucket_name = self.minio_bucket
                    # bucket_name/filename
                    html_uri = f"{bucket_name}/{filename}"
                    page_data = {
                        "page_id": page_id,
                        "title": title,
                        "html_uri": html_uri,
                    }
                    self.send_to_full_text_search_api(page_data)
                # Prepare response
                file_uri = meta["uri"] if meta else (existing_page.html_uri if existing_page else "")
                content_type = meta.get('content_type', 'text/html') if meta else existing_page.content_type if existing_page else 'text/html'
                
                # space home page id should not publish
                space_home_page_id = ["553845745"]

                if parent_id and parent_id not in space_home_page_id:
                    logger.info(f"Parent page {parent_id} found, publishing page {page_id}")
                    self.publish_page(parent_id, "html", cookies)
                else:
                    logger.warning(f"Parent page {parent_id} not found, skipping publish")
                
                # Refresh cache after publishing
                self.refresh_tree_cache(published_only=True)
                
                return {
                    "message": "Page published successfully",
                    "file_uri": file_uri,
                    "title": title,
                    "hash_content": hash_content,
                    "created_date": created_date,
                    "updated_date": updated_date, 
                    "page_id": page_id,
                    "is_content_changed": is_content_changed,
                    "content_type": content_type
                }
            
            except ValueError as e:
                logger.error(f"Validation error for page {page_id}: {e}")
                raise HTTPException(status_code=400, detail=str(e))
            except SQLAlchemyError as e:
                db.rollback()
                logger.error(f"Database error for page {page_id}: {e}")
                raise HTTPException(status_code=500, detail="Database operation failed")
            except Exception as e:
                db.rollback()
                logger.error(f"Unexpected error for page {page_id}: {e}", exc_info=True)
                raise HTTPException(status_code=500, detail="Internal server error")
            finally:
                operation_duration = (datetime.now() - operation_start).total_seconds()
                logger.info(f"Page {page_id} published in {operation_duration} seconds")
        
    def get_page_by_page_id(self, page_id: str):
        logger.info(f"Getting page by page_id: {page_id}")
        with get_session() as db:
            return page_repository.get_page_by_page_id(db, page_id)

    def delete_page(self, page_id: str):
        logger.info(f"Deleting page: {page_id}")
        try:
            with get_session() as db:
                # Lấy thông tin page trước khi xoá để có URI MinIO
                page_data = page_repository.get_page_by_page_id(db, page_id)
                if not page_data:
                    logger.warning(f"Page {page_id} not found for deletion")
                    return
                    
                # Lưu URI để xoá MinIO sau khi xoá DB
                html_uri = page_data.get('html_uri')
                
                # Xoá từ database
                page_repository.delete_page(db, page_id)
                
                # Update cache asynchronously after deleting
                self.refresh_tree_cache(published_only=True)
                
                # Xoá từ MinIO nếu có URI
                if html_uri:
                    try:
                        self.minio_client.remove_object(self.minio_bucket, html_uri)
                        logger.info(f"Deleted object {html_uri} from MinIO")
                    except S3Error as e:
                        logger.error(f"Failed to delete from MinIO: {str(e)}")
                
                # Xoá từ full-text search index
                page_data = {
                    "page_id": page_id,
                }
                self.send_to_full_text_search_api_delete(page_data)
        except Exception as e:
            logger.error(f"Error during page deletion: {str(e)}", exc_info=True)
            raise HTTPException(status_code=500, detail=f"Error deleting page: {str(e)}")
            
    def get_html_content_by_page_id(self, page_id: str) -> Tuple[bytes, str]:
        """Lấy nội dung HTML theo page_id từ PostgreSQL và MinIO
        
        Args:
            page_id: ID của trang cần lấy nội dung
            
        Returns:
            Tuple containing HTML content as bytes and content type string
            
        Raises:
            HTTPException: If page not found, HTML URI not found, or error fetching from MinIO
        """
        try:
            # Query PostgreSQL directly
            with get_session() as db:
                # Query PostgreSQL
                page_data = page_repository.get_page_by_page_id(db, page_id)
                if page_data is None:
                    raise HTTPException(status_code=404, detail="Page not found")
                if page_data.get('type') != 'page':
                    logger.warning(f"Page {page_id} is not a page")
                if page_data.get('is_published') == False:
                    raise HTTPException(status_code=404, detail="you can't access this page")

                uri_html = page_data.get('html_uri')
                if not uri_html:
                    raise HTTPException(status_code=404, detail="HTML URI not found for this page")

                try:
                    # Get object from MinIO
                    response = self.minio_client.get_object(self.minio_bucket, uri_html)
                    try:
                        html_content = response.read()
                        # content type
                        content_type = page_data.get('content_type') # improve content type
                        return html_content, content_type
                    finally:
                        response.close()
                        response.release_conn()
                except S3Error as err:
                    logger.error(f"Error fetching file from MinIO: {str(err)}")
                    raise HTTPException(status_code=500, detail=f"Error fetching file from MinIO: {str(err)}")
        except HTTPException:
            # Re-raise HTTP exceptions
            raise
        except Exception as e:
            logger.error(f"Error retrieving HTML content: {json.dumps(e, default=str)}")
            raise HTTPException(status_code=500, detail=f"Database error: {json.dumps(e, default=str)}")
    
    def send_to_full_text_search_api(self, page_data: Dict[str, Any]) -> None:
        """ở service phần publish_page thêm vào việc chuyển thông tin qua một api khác để search full text
        This method runs in a background thread so it won't block the main publishing process
        
        Args:
            page_data: Dictionary containing page information for indexing
        """
        try:
            # Convert dict to PageSearchData model
            if not page_data or not page_data.get('page_id') or not page_data.get('title') or not page_data.get('html_uri'):
                logger.warning(f"Invalid page data: {page_data}")
                return
            search_data = PageSearchData(
                document_id=page_data['page_id'],
                title=page_data['title'],
                url=page_data['html_uri'],
            )
            try:
            # Send the actual HTTP request to the full-text search API
                # send_to_index sẽ gọi response.json() và trả về dict
                response_data = self.search_service.send_to_index(search_data)
                
                # Kiểm tra response từ API
                if isinstance(response_data, dict) and response_data.get('status') == 'error':
                    logger.error(f"Background thread: Failed to index content in full-text search API. Response: {response_data}")
                else:
                    logger.info(f"Background thread: Successfully sent page {search_data.document_id} to full-text search API")
            except Exception as e:
                logger.exception(f"Background thread: Error sending request to full-text search API: {traceback.format_exc()}")   
        except Exception as e:
            logger.exception(f"Background thread: Error sending request to full-text search API: {traceback.format_exc()}")   
    
    def send_to_full_text_search_api_delete(self, page_data: Dict[str, Any]) -> None:
        """Send page data to full-text search API for deletion"""
        try:
            # Convert dict to PageDeleteData model
            if not page_data or not page_data.get('page_id'):
                logger.warning(f"Invalid page data: {page_data}")
                return
            delete_data = PageDeleteData(
                document_id=page_data['page_id']
            )
            
            # Send the actual HTTP request to the full-text search API
            try:
                # send_to_delete sẽ gọi response.json() và trả về dict
                response_data = self.search_service.send_to_delete(delete_data)
                
                # Kiểm tra response từ API
                if isinstance(response_data, dict) and response_data.get('status') == 'error':
                    logger.error(f"Background thread: Failed to delete content in full-text search API. Response: {response_data}")
                else:
                    logger.info(f"Background thread: Successfully sent page {delete_data.document_id} to full-text search API for deletion")
            except Exception as e:
                logger.exception(f"Background thread: Error sending request to full-text search API for deletion: {traceback.format_exc()}")
            
        except Exception as e:
            logger.exception(f"Background thread: Error sending content to full-text search API for deletion: {traceback.format_exc()}")
    
    def get_tree_pages(self, published_only: bool = True, force_refresh: bool = False) -> List[Dict[str, Any]]:
        """
        Get tree pages with caching using cache thought pattern
        
        Args:
            published_only: Whether to include only published pages
            force_refresh: If True, ignore cache and force a refresh
            
        Returns:
            List of pages in tree structure
        """
        
        # Calculate cache key
        cache_key = f"published_only_{published_only}"  # Thread-safe key generation
        
        # Try to get from cache first if not forcing refresh
        if not force_refresh and cache_key in self.tree_cache:
            logger.info(f"Returning tree pages from cache: {cache_key}")
            tree_pages = self.tree_cache[cache_key]
            return tree_pages
        
        logger.info(f"Updating tree pages cache: {cache_key} - force_refresh: {force_refresh}")
        
        begin_time = time.time()
        tree_pages = self.page_repo.get_tree_pages(published_only=published_only)
        logger.debug(f"Time to build tree pages: {time.time() - begin_time}")
        
        try:
            with self.cache_lock:
                self.tree_cache[cache_key] = tree_pages
                logger.debug(f"Updated tree pages cache for cache_key={cache_key} with {len(tree_pages) if tree_pages else 0} items")
            
        except Exception as e:
            logger.exception(f"Error updating tree pages cache: {e}")
        
        logger.debug(f"Time to update tree pages cache: {time.time() - begin_time}")
        return tree_pages
    
    def refresh_tree_cache(self, published_only: bool = True):
        """
        Refresh the cache by updating it with the latest data from the repository
        """
        try:
            self.get_tree_pages(published_only=published_only, force_refresh=True)
            logger.info(f"Tree pages cache refreshed")
        except Exception as e:
            logger.exception(f"Error refreshing tree pages cache: {e}")
            
page_service = PageService()