import logging
import sys
from config.config import settings

def setup_logging():
    log_level = settings.log_level.upper()

    handlers = [logging.StreamHandler(sys.stdout)]
    if settings.enable_file_log:
        handlers.append(logging.FileHandler("app.log", mode="a"))

    # Use a simpler format string that doesn't rely on specific Uvicorn log record attributes
    logging.basicConfig(
        level=log_level,
        format="%(asctime)s - %(levelname)s - %(name)s:%(lineno)d - %(message)s",
        handlers=handlers
    )
    
    # Fix for Uvicorn access logger
    uvicorn_access_logger = logging.getLogger("uvicorn.access")
    for handler in uvicorn_access_logger.handlers:
        handler.setFormatter(logging.Formatter("%(asctime)s - %(levelname)s - %(name)s:%(lineno)d - %(message)s"))

