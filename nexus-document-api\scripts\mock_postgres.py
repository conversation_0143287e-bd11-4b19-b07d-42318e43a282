import os
import sys
from datetime import datetime

# Đặt biến môi trường trước khi import module database
os.environ["POSTGRES_DB"] = "mydb"
os.environ["POSTGRES_HOST"] = "localhost"
os.environ["POSTGRES_PORT"] = "5432"
os.environ["POSTGRES_USER"] = "user"
os.environ["POSTGRES_PASSWORD"] = "password"

# Thêm đường dẫn src vào sys.path để import các module
src_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if src_path not in sys.path:
    sys.path.append(src_path)

# Import các module sau khi đặt biến môi trường
from data.repositories.page_repository2 import get_tree_pages, insert_page, get_page_by_page_id, invalidate_and_reload_tree_cache
from data.db_connection import get_session
from data.db_connection import init_db

# Khởi tạo database
init_db()

# Use session directly
with get_session() as db:
    # mock 20 pages
    for i in range(1, 21):
        page_id = str(i)  # Đổi sang string vì đã cập nhật kiểu của page_id
        page_data = {
            "title": f"Test Page {i}",
            "parent_id": None if i == 1 else "1",  # Đổi sang string
            "space_id": "test-space",
            "page_id": page_id,
            "html_uri": f"test-page-{i}.html",
            "created_date": datetime.now(),
            "updated_date": datetime.now(),
            "status": "active",
            "hash_content": f"test-{i}",  # Đổi từ hash_content sang content_hash
            "is_published": True,
            "type": "page",
            "content_type": "text/html"
        }
        
        insert_page(db, page_data)
        print(f"Page {page_id} inserted successfully.")
    for i in range(21, 31):
        page_id = str(i)  # Đổi sang string vì đã cập nhật kiểu của page_id
        page_data = {
            "title": f"Test Page {i}",
            "parent_id": None if i == 1 else "2",  # Đổi sang string
            "space_id": "test-space",
            "page_id": page_id,
            "html_uri": f"test-page-{i}.html",
            "created_date": datetime.now(),
            "updated_date": datetime.now(),
            "status": "active",
            "hash_content": f"test-{i}",  # Đổi từ hash_content sang content_hash
            "is_published": False,
            "type": "page",
            "content_type": "text/html"
        }
        
        insert_page(db, page_data)
        print(f"Page {page_id} inserted successfully.")
    for i in range(31, 41):
        page_id = str(i)  # Đổi sang string vì đã cập nhật kiểu của page_id
        page_data = {
            "title": f"Test Page {i}",
            "parent_id": None if i == 1 else "3",  # Đổi sang string
            "space_id": "test-space",
            "page_id": page_id,
            "html_uri": f"test-page-{i}.html",
            "created_date": datetime.now(),
            "updated_date": datetime.now(),
            "status": "active",
            "hash_content": f"test-{i}",  # Đổi từ hash_content sang content_hash
            "is_published": False,
            "type": "folder",
            "content_type": "text/html"
        }
        
        insert_page(db, page_data)
        print(f"Page {page_id} inserted successfully.")

    for i in range(41, 51):
        page_id = str(i)  # Đổi sang string vì đã cập nhật kiểu của page_id
        page_data = {
            "title": f"Test Page {i}",
            "parent_id": None if i == 1 else "4",  # Đổi sang string
            "space_id": "test-space",
            "page_id": page_id,
            "html_uri": f"test-page-{i}.html",
            "created_date": datetime.now(),
            "updated_date": datetime.now(),
            "status": "active",
            "hash_content": f"test-{i}",  # Đổi từ hash_content sang content_hash
            "is_published": False,
            "type": "page",
            "content_type": "text/markdown"
        }
        
        insert_page(db, page_data)
        print(f"Page {page_id} inserted successfully.")
    # Kiểm tra dữ liệu đã thêm vào
    invalidate_and_reload_tree_cache()
    tree_pages = get_tree_pages()
    print(f"\nTổng số trang trong tree: {len(tree_pages)}")
    print(f"Trang đầu tiên: {tree_pages[0]['title']} (ID: {tree_pages[0]['page_id']})")
    
    # Lấy một trang theo ID
    sample_page = get_page_by_page_id(db, "5")
    if sample_page:
        print(f"\nThông tin trang ID=5: {sample_page['title']}")
        print(f"HTML URI: {sample_page['html_uri']}")

