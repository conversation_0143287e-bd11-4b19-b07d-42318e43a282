# This file should be renamed or modified to avoid conflicts with database1.py
# If you need to keep both files, ensure they don't define the same tables

import os
from sqlalchemy import create_engine, Column, Integer, String, Text, ForeignKey, DateTime, MetaData
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from datetime import datetime
from dotenv import load_dotenv

# Use the existing Base and metadata instead of creating new ones
# This way, tables are only defined once

# If you need to define additional tables that are only in database.py,
# you can do so here using the shared Base and metadata

# Initialize database - use the same function as in database1.py

load_dotenv()

POSTGRES_DB = os.getenv("POSTGRES_DB")
POSTGRES_HOST = os.getenv("POSTGRES_HOST")
POSTGRES_PORT = os.getenv("POSTGRES_PORT")
POSTGRES_USER = os.getenv("POSTGRES_USER")
POSTGRES_PASSWORD = os.getenv("POSTGRES_PASSWORD")

Base = declarative_base()
DATABASE_URL = f"postgresql://{POSTGRES_USER}:{POSTGRES_PASSWORD}@{POSTGRES_HOST}:{POSTGRES_PORT}/{POSTGRES_DB}"

engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def init_db():
    Base.metadata.create_all(bind=engine)

# Get database session - use the same function as in database1.py
def get_session():
    session = SessionLocal()
    return session

# Close database session
def close_session(session):
    session.close()
