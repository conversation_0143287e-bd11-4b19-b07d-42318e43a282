from math import log
import requests
from requests.auth import HTTP<PERSON>asicAuth
from dotenv import load_dotenv
import os
import logging

logger = logging.getLogger(__name__)

load_dotenv(override=True)

CONFLUENCE_URL = os.getenv("CONFLUENCE_URL")
CONFLUENCE_TOKEN = os.getenv("CONFLUENCE_TOKEN")
CONFLUENCE_EMAIL = os.getenv("CONFLUENCE_EMAIL")

def get_confluence_page(page_id):
    url = f"{CONFLUENCE_URL}/wiki/rest/api/content/{page_id}?expand=body.export_view,version.by,history.createdBy,ancestors,space"
    response = requests.get(
        url,
        auth=HTTPBasicAuth(CONFLUENCE_EMAIL, CONFLUENCE_TOKEN),
        headers={"Accept": "application/json"}
    )
    
    if response.status_code == 200:
        logger.info(f"Get page {page_id} from Confluence")
        logger.debug(f"Get page {page_id} from Confluence: {response.json()}")
        return response.json()
    else:
        logger.error(f"Failed to get page {page_id} from Confluence with status code {response.status_code}")
        return None
