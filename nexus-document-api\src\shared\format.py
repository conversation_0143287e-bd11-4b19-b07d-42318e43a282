import unicodedata
import re

def convert_to_grow_segment(text):
    # Loại bỏ dấu tiếng Việt
    text = unicodedata.normalize('NFD', text)
    text = text.encode('ascii', 'ignore').decode('utf-8')
    
    # Loại bỏ khoảng trắng đầu/cuối và thay khoảng trắng, dấu gạch ngang bằng dấu gạch dưới
    text = re.sub(r'\s*-\s*', '_', text.strip())  # thay dấu " - " bằng "_"
    text = re.sub(r'\s+', '_', text)              # thay các khoảng trắng còn lại bằng "_"
    
    return text