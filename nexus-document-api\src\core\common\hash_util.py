from hashlib import md5, sha256
from typing import Any

def md5_hash(content: Any):
    if isinstance(content, bytes):
        return md5(content).hexdigest()
    if isinstance(content, str):
        return md5(content.encode()).hexdigest()
    return md5(str(content).encode()).hexdigest()

def sha256_hash(content: Any):
    if isinstance(content, bytes):
        return sha256(content).hexdigest()
    if isinstance(content, str):
        return sha256(content.encode()).hexdigest()
    return sha256(str(content).encode()).hexdigest()