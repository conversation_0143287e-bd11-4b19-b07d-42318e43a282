# Background Processing và Full-Text Search Integration

## Tổng quan

Tài liệu này mô tả việc triển khai tính năng xử lý nền (background processing) cho việc gửi dữ liệu đến full-text search API sau khi publish một trang, cũng như API mới để hỗ trợ tìm kiếm văn bản toàn văn (full-text search).

## Các tính năng đã thêm

### 1. Background Tasks cho Full-Text Search Indexing

Đã triển khai việc gửi dữ liệu đến Full-Text Search API trong background task sau khi trang được publish thành công, giúp:

- Tối ưu thời gian phản hồi API bằng cách không phải chờ đợi quá trình indexing hoàn tất
- Tránh lỗi publish trang khi full-text search API không khả dụng
- Ghi log chi tiết về quá trình indexing trong background

**C<PERSON>ch thực hiện**:
- <PERSON><PERSON> dụng `BackgroundTasks` của FastAPI trong `markdown_routes.py` để gọi `_send_to_full_text_search_api` 
- Chỉ gửi dữ liệu đến full-text search API khi nội dung trang thay đổi (kiểm tra bằng content hash)
- Khi chạy trong background task, phương thức `_send_to_full_text_search_api` sẽ tự lấy nội dung HTML từ MinIO

### 2. Full-Text Search Content API 

Thêm API endpoint mới `/api/full-text/content` cho phép lấy nội dung HTML và tiêu đề của nhiều trang theo danh sách page_id:

- Hỗ trợ lấy dữ liệu hàng loạt thông qua một request duy nhất
- Trả về nội dung HTML và tiêu đề của trang để có thể sử dụng cho full-text search
- Xử lý lỗi riêng cho từng trang, không làm ảnh hưởng đến việc xử lý các trang khác trong cùng request
- Phân tích và chuyển đổi nội dung bytes thành string với xử lý lỗi encoding

## Thông tin kỹ thuật

### Flow của quá trình publish và indexing

1. Client gọi API `/api/publish` với page_id
2. API xử lý đồng bộ việc publish trang (lấy dữ liệu, lưu vào MinIO, cập nhật DB)
3. Nếu nội dung thay đổi (dựa vào content hash), thêm background task để gửi dữ liệu đến full-text search API
4. API trả về kết quả publish thành công không đợi background task hoàn thành
5. Background task chạy độc lập, lấy nội dung HTML từ MinIO và gửi đến full-text search API

### Chi tiết triển khai

#### Trong `markdown_routes.py`

```python
@router.post("/api/publish", response_model=PublishResponse)
async def publish_md(
    request: Request,
    background_tasks: BackgroundTasks,
    page_id: str = Form(...),
    # ... other parameters
):
    # ... Publish process
    
    # If content changed, add background task
    if result and is_content_changed:
        page_data = {
            "page_id": page_id,
            "title": result.get("title", ""),
            "html_uri": result.get("file_uri", ""),
            "content_hash": result.get("content_hash", ""),
        }
        
        # Add background task for full-text search indexing
        background_tasks.add_task(page_service._send_to_full_text_search_api, page_data)
```

#### Trong `page_service.py`

```python
def _send_to_full_text_search_api(self, page_data: Dict[str, Any]) -> None:
    """Gửi thông tin trang đến full-text search API
    
    Args:
        page_data: Dictionary containing page information for indexing
    """
    try:
        logger.info(f"Background thread: Sending page {page_data.get('page_id')} to full-text search API")
        search_api_url = "http://127.0.0.1:9000/api/index"
        
        # Lấy nội dung HTML từ MinIO nếu cần
        if page_data.get("html_uri"):
            try:
                html_content, _ = self.get_html_content_by_page_id(page_data["page_id"])
                page_data["html_content"] = html_content.decode("utf-8", errors="replace")
            except Exception as e:
                logger.warning(f"Could not fetch HTML content for full-text indexing: {str(e)}")
        
        # Gửi HTTP request đến full-text search API
        response = requests.post(
            search_api_url,
            json=page_data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        if response.status_code != 200:
            logger.error(f"Failed to index content in full-text search API: {response.status_code}")
        else:
            logger.info(f"Successfully sent page to full-text search API")
            
    except Exception as e:
        # Log lỗi nhưng không ảnh hưởng đến quá trình publish
        logger.error(f"Error in full-text search API: {str(e)}")
```

#### Trong `full_text_search_route.py`

```python
@router.post("/api/full-text/content", response_model=List[PageContent])
async def get_content_for_indexing(page_list: PageIdList = Body(...)):
    """Lấy nội dung HTML và tiêu đề của nhiều trang theo danh sách page_id
    """
    page_service = PageService()
    results = []
    
    for page_id in page_list.page_ids:
        try:
            # Lấy thông tin trang và HTML content
            page_info = page_service.get_page_by_page_id(page_id)
            html_content, _ = page_service.get_html_content_by_page_id(page_id)
            
            results.append(PageContent(
                page_id=page_id,
                title=page_info.get('title'),
                html_content=html_content.decode('utf-8', errors='replace')
            ))
        except Exception as e:
            # Xử lý lỗi riêng cho từng trang
            results.append(PageContent(
                page_id=page_id,
                error=str(e)
            ))
    
    return results
```

## Cách test các tính năng

### Test Background Processing:

1. Chạy mock server: `python mock_server.py`
2. Publish một trang qua API `/api/publish` 
3. Kiểm tra logs của mock server để xác nhận dữ liệu đã được gửi đến full-text search API

### Test API `/api/full-text/content`:

1. Chạy script test: `python sample_request.py <page_id_1> <page_id_2> ...`
2. Kiểm tra kết quả trả về bao gồm tiêu đề và nội dung HTML của các trang

## Lưu ý và khuyến nghị

- **Môi trường production**: URL của full-text search API nên được cấu hình trong file config hoặc environment variables thay vì hardcode trong code
- **Xử lý lỗi**: Nên triển khai retry mechanism cho background task khi gặp lỗi tạm thời
- **Monitoring**: Thêm metrics để theo dõi tỷ lệ thành công/thất bại của việc gửi dữ liệu đến full-text search API
- **Performance testing**: Đo lường thời gian phản hồi API trước và sau khi triển khai background processing để xác nhận cải thiện hiệu suất

## Kết luận

Việc triển khai background processing cho full-text search indexing giúp cải thiện đáng kể hiệu suất của API publish trang, đồng thời đảm bảo khả năng mở rộng và độ tin cậy của hệ thống. API full-text/content mới cung cấp cách hiệu quả để lấy nội dung cần thiết cho tìm kiếm văn bản toàn văn.
