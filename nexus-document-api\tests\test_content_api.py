import pytest
from unittest.mock import patch, MagicMock
from flask import Flask
from api.rest.user_routes import user_bp
from io import BytesIO


@pytest.fixture
def app():
    app = Flask(__name__)
    app.register_blueprint(user_bp)
    app.minio_client = MagicMock()
    app.minio_bucket = "test-bucket"
    return app

@pytest.fixture
def client(app):
    return app.test_client()


@patch("business.services.user_service.UserService.get_page_by_page_id")
def test_get_html_content_by_page_id(mock_get_page, app, client):
    # Mock the response from UserService
    mock_get_page.return_value = {
        "id": "1",
        "title": "Test Page",
        "html_uri": "test-page.html",
        "content_type": "text/html",
        "content": "<html><body>Test Content</body></html>",
        "created_date": "2023-10-01T00:00:00Z",
        "author": "test_user",
        "status": "published"
    }
    
    # Mock MinIO client response
    mock_minio_response = MagicMock()
    mock_minio_response.read.return_value = b"<html><body>Test Content</body></html>"
    mock_minio_response.close.return_value = None
    mock_minio_response.release_conn.return_value = None

    app.minio_client.get_object.return_value = mock_minio_response
    
    response = client.get('/api/content/1', headers={"Authorization": "Bearer test_token"})
    
    assert response.status_code == 200
    assert response.content_type == 'text/html'
    assert b'Test Content' in response.data

# error html content not found
@patch("business.services.user_service.UserService.get_page_by_page_id")
def test_get_html_content_by_page_id_not_found(mock_get_page, client):
    # Mock the response from UserService to return None
    mock_get_page.return_value = None
    
    response = client.get('/api/content/1', headers={"Authorization": "Bearer test_token"})
    assert response.status_code == 404
    assert b'Page not found' in response.data

# error minio get object
@patch("business.services.user_service.UserService.get_page_by_page_id")
def test_get_html_content_minio_error(mock_get_page, app, client):
    # Mock the response from UserService
    mock_get_page.return_value = {
        "id": "1",
        "title": "Test Page",
        "html_uri": "test-page.html",
        "content_type": "text/html",
        "content": "<html><body>Test Content</body></html>",
        "created_date": "2023-10-01T00:00:00Z",
        "author": "test_user",
        "status": "published"
    }
    
    # Simulate MinIO error
    app.minio_client.get_object.side_effect = Exception("MinIO error")
    
    response = client.get('/api/content/1', headers={"Authorization": "Bearer test_token"})
    
    assert response.status_code == 500
    assert b'Error fetching file from MinIO' in response.data

# test list_pages
@patch("business.services.user_service.UserService.list_pages")
def test_list_pages(mock_list_pages, client):
    # Mock the response from UserService
    mock_list_pages.return_value = {
        "pages": [
            {"id": 1, "title": "Page 1", "html_uri": "uri1", "status": "published"},
            {"id": 2, "title": "Page 2", "html_uri": "uri2", "status": "draft"}
        ],
        "total_items": 2,
        "current_page": 1,
        "total_pages": 1
    }
    
    response = client.get('/api/list_pages', headers={"Authorization": "Bearer test_token"})
    
    assert response.status_code == 200
    assert b'Page 1' in response.data
    assert b'Page 2' in response.data

# error list_pages not found
@patch("business.services.user_service.UserService.list_pages")
def test_list_pages_not_found(mock_list_pages, client):
    # Mock the response from UserService to return an empty list
    mock_list_pages.return_value = {
        "pages": [],
        "total_items": 0,
        "current_page": 1,
        "total_pages": 0
    }
    
    response = client.get('/api/list_pages', headers={"Authorization": "Bearer test_token"})
    
    assert response.status_code == 200
    assert b'"pages": []' in response.data or b'pages":[]' in response.data

# missing authorization header
@patch("business.services.user_service.UserService.list_pages")
def test_list_pages_missing_auth(mock_list_pages, client):
    response = client.get('/api/list_pages')
    
    assert response.status_code == 401
    assert b'Authorization token is required' in response.data