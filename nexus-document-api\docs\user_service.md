# 👤 User Service Documentation

## Overview

## Features

- 📄 **Page Management**: List and retrieve pages with filtering capabilities
- 🔒 **Authentication**: Validate user access through authorization tokens
- 🔍 **Content Retrieval**: Get page content and metadata

## Service Methods

### `list_pages(authorization, parent_id)`

Lists all pages for the authenticated user with optional filtering by parent ID.

**Parameters:**
- `authorization` (str): Authorization token for user authentication
- `parent_id` (Optional[int]): Filter pages by parent ID (optional)

**Returns:**
- List of page objects with metadata (id, title, html_uri, page_id, parent_id, space_id, created_date, updated_date, status)

**Example:**
```python
# Get all pages under parent ID 1
pages = user_service.list_pages(authorization="Bearer token123", parent_id=1)
```
**Example answer:**
```json
[
    {
        "id": 1,
        "title": "Test Page 1",
        "html_uri": "test-page-1.html",
        "page_id": 1,
        "parent_id": null,
        "space_id": "thinhdeptrai",
        "created_date": "2025-05-26T15:23:37.757706",
        "updated_date": "2025-05-26T15:23:37.757707",
        "status": "active"
    },
    {
        "id": 2,
        "title": "Test Page 2",
        "html_uri": "test-page-2.html",
        "page_id": 2,
        "parent_id": 1,
        "space_id": "thinhdeptrai",
        "created_date": "2025-05-26T15:23:37.757706",
        "updated_date": "2025-05-26T15:23:37.757707",
        "status": "active"
    }
]
```
### `get_page_by_page_id(authorization, page_id)`

Retrieves a specific page by its ID for the authenticated user.

**Parameters:**
- `authorization` (str): Authorization token for user authentication
- `page_id` (int): ID of the page to retrieve

**Returns:**
- Page object with metadata (id, title, html_uri, page_id, parent_id, space_id, created_date, updated_date, status)
- sum with content (content, content_type) from MinIO
**Example:**
```python
# Get page with ID 5
page = user_service.get_page_by_page_id(authorization="Bearer token123", page_id=5)
```
**Example answer:**
```json
{
    "id": 5,
    "title": "Test Page 5",
    "html_uri": "test-page-5.html",
    "page_id": 5,
    "parent_id": 1,
    "space_id": "thinhdeptrai",
    "created_date": "2025-05-26T15:23:37.757706",
    "updated_date": "2025-05-26T15:23:37.757707",
    "status": "active"
    "content": "<h1>Test Page 5</h1><p>This is the content of test page 5.</p>",
    "content_type": "text/html"
}
```

## API Integration

The User Service is integrated with the API through the following routes:

### GET `/api/list_pages`

Lists all pages with optional filtering by parent ID.

**Query Parameters:**
- `parent_id` (Optional): Filter pages by parent ID

**Headers:**
- `Authorization`: Bearer token for authentication

**Response:**
```json
[
  {
    "id": 1,
    "title": "Test Page 1",
    "html_uri": "test-page-1.html",
    "page_id": 1,
    "parent_id": null,
    "space_id": "thinhdeptrai",
    "created_date": "2025-05-26T15:23:37.757706",
    "updated_date": "2025-05-26T15:23:37.757707",
    "status": "active"
  },
  ...
]
```

### GET `/api/content/{page_id}`

Retrieves the content and metadata of a specific page.

**Path Parameters:**
- `page_id`: ID of the page to retrieve

**Headers:**
- `Authorization`: Bearer token for authentication

**Response:**
```json
{
  "id": 2,
  "title": "Test Page 2",
  "html_uri": "test-page-2.html",
  "page_id": 2,
  "parent_id": 1,
  "space_id": "thinhdeptrai",
  "created_date": "2025-05-26T15:23:37.757706",
  "updated_date": "2025-05-26T15:23:37.757707",
  "status": "active",
  "content": "<h1>Test Page 2</h1><p>This is the content of test page 2.</p>",
  "content_type": "text/html"
}
```

## Error Handling

The User Service provides meaningful error messages for common issues:

- **401 Unauthorized**: Authorization token is missing or invalid
- **404 Not Found**: Page not found or does not exist
- **500 Internal Server Error**: Error fetching file from MinIO or other server errors

## Implementation Details

The User Service is implemented in the `business/services/user_service.py` file and relies on the following components:

- **SQLAlchemy ORM**: For database operations
- **Page Repository**: For retrieving page data from the database
- **MinIO Client**: For retrieving HTML content from object storage

## Security Considerations

- All endpoints require authentication via Bearer token
- Authorization is validated for each request
- Database connections are properly managed and closed after use
