from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Request, Response, status
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
import json
from typing import Dict, Any, List, Optional
from pydantic import BaseModel
import uuid
import time
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="Confluence Mock API")

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# In-memory storage for pages
# Matching the expected Confluence API structure
mock_pages = {
    "1": {
        "id": "1",
        "title": "Test Page",
        "type": "page",
        "space": {"key": "TEST", "id": "101", "name": "Test Space"},
        "body": {
            "storage": {"value": "<p>Test content 1</p>", "representation": "storage"},
            "export_view": {"value": "<p>Test content 1</p>", "representation": "export_view"}
        },
        "version": {"number": 1},
        "ancestors": [
            {"id": "10", "title": "Parent Page", "type": "page"}
        ],
        "status": "current",
        "history": {"createdBy": {"displayName": "Mock Creator", "username": "mockcreator"}},
        "_links": {"self": "http://mock-confluence/wiki/rest/api/content/1"}
    },
    "2": {
        "id": "2",
        "title": "Test Page 2",
        "type": "page",
        "space": {"key": "TEST", "id": "101", "name": "Test Space"},
        "body": {
            "storage": {"value": "<p>Test content 5</p>", "representation": "storage"},
            "export_view": {"value": "<p>Test content 5</p>", "representation": "export_view"}
        },
        "version": {"number": 1},
        "ancestors": [],
        "status": "current",
        "history": {"createdBy": {"displayName": "Mock Creator", "username": "mockcreator"}},
        "_links": {"self": "http://mock-confluence/wiki/rest/api/content/2"}
    },
    "3": {
        "id": "3",
        "title": "Test Page 3",
        "type": "page",
        "space": {"key": "TEST", "id": "101", "name": "Test Space"},
        "body": {
            "storage": {"value": "<p>Test content 3</p>", "representation": "storage"},
            "export_view": {"value": "<p>Test content 3</p>", "representation": "export_view"}
        },
        "version": {"number": 1},
        "ancestors": [
            {"id": "2", "title": "Test Page 2", "type": "page"}
        ],
        "status": "current",
        "history": {"createdBy": {"displayName": "Mock Creator", "username": "mockcreator"}},
        "_links": {"self": "http://mock-confluence/wiki/rest/api/content/3"}
    }
}

webhook_calls = []

# Models
class WebhookEvent(BaseModel):
    event: str
    page_id: str
    space_key: Optional[str] = None
    title: Optional[str] = None
    content: Optional[str] = None

class Page(BaseModel):
    id: str
    title: str
    space: Dict[str, Any]
    body: Dict[str, Any]
    version: Dict[str, int]
    status: str = "current"

@app.get("/")
async def root():
    return {"message": "Confluence Mock API running"}

@app.get("/wiki/rest/api/content/{page_id}")
async def get_content(page_id: str, expand: Optional[str] = None):
    """Get content from mock Confluence with exact URL structure"""
    if page_id not in mock_pages:
        raise HTTPException(status_code=404, detail="Page not found")
    
    result = mock_pages[page_id].copy()
    
    # Xử lý tham số expand và đảm bảo các trường cần thiết tồn tại
    # theo đúng cấu trúc mà get_confluence_page đang mong đợi
    
    # Đảm bảo các trường cơ bản luôn tồn tại
    if "type" not in result:
        result["type"] = "page"
        
    # Đảm bảo space có id
    if "space" not in result or not isinstance(result["space"], dict):
        result["space"] = {"key": "TEST", "id": "101"}
    elif "id" not in result["space"]:
        result["space"]["id"] = "101"
        
    # Xử lý tham số expand
    if expand:
        expand_fields = expand.split(",")
        
        # Xử lý body.export_view - cần thiết cho việc trích xuất nội dung
        if "body.export_view" in expand_fields and "body" in result:
            if "export_view" not in result["body"]:
                storage_value = result["body"].get("storage", {}).get("value", "<p>Default content</p>")
                result["body"]["export_view"] = {
                    "value": storage_value,
                    "representation": "export_view"
                }
                
        # Xử lý version.by
        if "version.by" in expand_fields:
            if "version" not in result:
                result["version"] = {"number": 1}
            result["version"]["by"] = {"displayName": "Mock User", "username": "mockuser"}
            
        # Xử lý history.createdBy
        if "history.createdBy" in expand_fields:
            result["history"] = {"createdBy": {"displayName": "Mock Creator", "username": "mockcreator"}}
            
        # Xử lý ancestors
        if "ancestors" in expand_fields and "ancestors" not in result:
            result["ancestors"] = []
    
    # Đảm bảo ancestors tồn tại ngay cả khi không có trong expand
    if "ancestors" not in result:
        result["ancestors"] = []
        
    logger.info(f"Serving mock Confluence content for page_id={page_id}, expand={expand}")
    
    return result

# Add a webhook simulator endpoint
@app.post("/api/simulate/webhook/{event_type}")
async def simulate_webhook(event_type: str, request: Request):
    """Simulate a webhook event from Confluence"""
    valid_events = ["page_created", "page_updated", "page_deleted"]
    if event_type not in valid_events:
        raise HTTPException(status_code=400, detail=f"Invalid event type. Must be one of: {', '.join(valid_events)}")
    
    try:
        payload = await request.json()
    except json.JSONDecodeError:
        logger.warning("No valid JSON in request body or empty body, using default values")
        payload = {}
    page_id = payload.get("page_id", str(uuid.uuid4()))
    
    # Endpoint to call on the main app
    target_url = payload.get("target_url", "http://markdown-publisher:5000/api/v1/nexus-docs/webhook/confluence/events")
    
    # Create webhook event payload based on ConfluenceWebhookEvent model expected by the handler
    mock_title = payload.get("title", f"Test Page {page_id}")
    mock_content = payload.get("content", "<p>Test content</p>")
    mock_space_key = payload.get("space_key", "TEST")
    
    # Map event type from simplified to Confluence format
    update_trigger = "create_page"
    if event_type == "page_updated":
        update_trigger = "edit_page"
    elif event_type == "page_deleted":
        update_trigger = "delete_page"
    
    # Create payload matching the ConfluenceWebhookEvent expected structure
    event_payload = {
        "page": {
            "id": page_id,
            "title": mock_title,
            "spaceKey": mock_space_key,
            "idAsString": page_id,
            "contentType": "html",
            "type": "page",
            "version": 1,
            "creationDate": int(time.time() * 1000),
            "modificationDate": int(time.time() * 1000),
            "creatorAccountId": "mock-user-id",
            "lastModifierAccountId": "mock-user-id",
            "self": f"http://mock-confluence/wiki/rest/api/content/{page_id}"
        },
        "userAccountId": "mock-user-id",
        "accountType": "atlassian",
        "timestamp": int(time.time() * 1000),
        "updateTrigger": update_trigger,
        "suppressNotifications": False
    }
    
    # QUAN TRỌNG: Tự động tạo hoặc cập nhật trang trong mock_pages để page_service.publish_page có thể lấy được
    if event_type in ["page_created", "page_updated"]:
        # Cập nhật mock_pages để webhook hoạt động đúng khi gọi lại Confluence API
        mock_pages[page_id] = {
            "id": page_id,
            "title": mock_title,
            "type": "page",
            "space": {"key": mock_space_key, "id": "101", "name": f"{mock_space_key} Space"},
            "body": {
                "storage": {"value": mock_content, "representation": "storage"},
                "export_view": {"value": mock_content, "representation": "export_view"}
            },
            "version": {"number": 1},
            "ancestors": [],
            "status": "current",
            "history": {"createdBy": {"displayName": "Mock Creator", "username": "mockcreator"}},
            "_links": {"self": f"http://mock-confluence/wiki/rest/api/content/{page_id}"}
        }
        logger.info(f"Auto-created mock page in Confluence mock for webhook: {page_id}")
    
    # Store content separately for webhook history, but it's not part of standard payload
    webhook_content = None
    if event_type in ["page_created", "page_updated"]:
        webhook_content = mock_content
    
    logger.info(f"Simulating {event_type} webhook to {target_url} with payload: {event_payload}")
    
    # Thực sự gọi webhook đến ứng dụng chính
    try:
        import requests
        response = requests.post(target_url, json=event_payload)
        response_data = {
            "status_code": response.status_code,
            "response_text": response.text,
            "event_type": event_type,
            "target_url": target_url
        }
        logger.info(f"Webhook response: {response.status_code} - {response.text}")
    except Exception as e:
        logger.error(f"Error calling webhook: {str(e)}")
        response_data = {
            "error": str(e),
            "event_type": event_type,
            "target_url": target_url
        }
    
    webhook_calls.append({
        "timestamp": time.time(),
        "target_url": target_url,
        "payload": event_payload,
        "response": response_data
    })
    
    return {
        "status": "simulated",
        "event_type": event_type, 
        "target_url": target_url,
        "payload": event_payload,
        "response": response_data
    }

@app.get("/api/webhook/history")
async def get_webhook_history():
    """Truy xuất lịch sử các webhook đã gọi"""
    return webhook_calls

@app.get("/api/test/status")
async def test_system_status():
    """Kiểm tra trạng thái của hệ thống mock"""
    # Thử kết nối đến markdown-publisher để kiểm tra connectivity
    try:
        import requests
        response = requests.get("http://markdown-publisher:5000/api/v1/nexus-docs/")
        main_app_status = {
            "status": "connected" if response.status_code == 200 else "error",
            "status_code": response.status_code,
            "response_text": response.text
        }
    except Exception as e:
        main_app_status = {"status": "error", "message": str(e)}
    
    return {
        "mock_api_status": "running",
        "main_app_status": main_app_status,
        "mock_pages_count": len(mock_pages),
        "webhook_calls_count": len(webhook_calls)
    }
if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000, log_level="info")