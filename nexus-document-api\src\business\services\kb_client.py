import requests
import os
from dotenv import load_dotenv
import logging

logger = logging.getLogger("kb_client")

load_dotenv(override=True)

CONFLUENCE_URL = os.getenv("CONFLUENCE_URL")
CONFLUENCE_TOKEN = os.getenv("CONFLUENCE_TOKEN")
CONFLUENCE_EMAIL = os.getenv("CONFLUENCE_EMAIL")
KB_PUBLISHER_SERVICE = os.getenv("KB_PUBLISHER_SERVICE")
KB_COLLECTION_ID = os.getenv("KB_COLLECTION_ID")
NEXUS_DOCS_PUBLIC_URL = os.getenv("NEXUS_DOCS_PUBLIC_URL")

def sync_to_kb(space_id, page_id):
    url = f"{KB_PUBLISHER_SERVICE}/api/ingestor/confluence/sync"
    payload = {
        "collection_id": KB_COLLECTION_ID,
        "page_id": page_id,
        "confluence_url": CONFLUENCE_URL,
        "api_token": CONFLUENCE_TOKEN,
        "email": CONFLUENCE_EMAIL,
        "max_pages": 1,
        "space_id": space_id,
        "include_attachments": True,
        "ref_url": NEXUS_DOCS_PUBLIC_URL if NEXUS_DOCS_PUBLIC_URL else None,
        "updated_by": "nexus-docs-admin"
    }
    logger.info("Curl to KB: " + url + " with payload: " + str(payload))
    response = requests.post(
        url,
        headers={"Accept": "application/json"},
        json=payload
    )
    
    if not response:
        logger.error("Failed to sync to KB")
        raise Exception("Failed to sync to KB")

    if response.status_code != 200:
        logger.error("Failed to sync to KB: " + response.json() if response.json() else "Empty response")
        return False
    
    logger.info(f"Synced to KB page {CONFLUENCE_URL} - {space_id} - {page_id} successfully")
    return True